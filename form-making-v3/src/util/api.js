import request from './request'

// 表单相关API
export const saveForm = (formData) => {
  return request.post('/api/forms', formData)
}

export const updateForm = (id, formData) => {
  return request.put(`/api/forms/${id}`, formData)
}

export const getFormList = (params) => {
  return request.get('/api/forms', { params })
}

export const getFormById = (id) => {
  return request.get(`/api/forms/${id}`)
}

export const deleteForm = (id) => {
  return request.delete(`/api/forms/${id}`)
}

// 获取表单模板列表
export const getFormTemplates = (params = {}) => {
  return request.get('/api/forms', { params })
}

// 字典管理相关API
// 字典分类
export const getDictionaryCategories = () => {
  return request.get('/api/dictionaries/categories')
}

export const createDictionaryCategory = (data) => {
  return request.post('/api/dictionaries/categories', data)
}

export const updateDictionaryCategory = (id, data) => {
  return request.put(`/api/dictionaries/categories/${id}`, data)
}

export const deleteDictionaryCategory = (id) => {
  return request.delete(`/api/dictionaries/categories/${id}`)
}

// 字典管理
export const getDictionaries = (params = {}) => {
  return request.get('/api/dictionaries', { params })
}

export const getDictionary = (id) => {
  return request.get(`/api/dictionaries/${id}`)
}

export const createDictionary = (data) => {
  return request.post('/api/dictionaries', data)
}

export const updateDictionary = (id, data) => {
  return request.put(`/api/dictionaries/${id}`, data)
}

export const deleteDictionary = (id) => {
  return request.delete(`/api/dictionaries/${id}`)
}

export const getDictionaryUsage = (id) => {
  return request.get(`/api/dictionaries/${id}/usage`)
}

export const getDictionaryVersions = (id) => {
  return request.get(`/api/dictionaries/${id}/versions`)
}

export const createDictionaryVersion = (id, data) => {
  return request.post(`/api/dictionaries/${id}/versions`, data)
}

export const deleteDictionaryVersion = (dictionaryId, versionId) => {
  return request.delete(`/api/dictionaries/${dictionaryId}/versions/${versionId}`)
}

export const rollbackDictionaryVersion = (dictionaryId, versionId) => {
  return request.post(`/api/dictionaries/${dictionaryId}/versions/${versionId}/rollback`)
}

// 根据编码获取字典（供表单使用）
export const getDictionaryByCode = (code, categoryCode = null) => {
  const params = categoryCode ? { category_code: categoryCode } : {}
  return request.get(`/api/dictionaries/code/${code}`, { params })
}

// ==================== 列表视图 API ====================

// 获取列表视图列表
export const getListViews = (params = {}) => {
  return request.get('/api/list-views', { params })
}

// 获取单个列表视图详情
export const getListView = (id) => {
  return request.get(`/api/list-views/${id}`)
}

// 根据编码获取列表视图详情
export const getListViewByCode = (code) => {
  return request.get(`/api/list-views/code/${code}`)
}

// 创建列表视图
export const createListView = (data) => {
  return request.post('/api/list-views', data)
}

// 更新列表视图
export const updateListView = (id, data) => {
  return request.put(`/api/list-views/${id}`, data)
}

// 删除列表视图
export const deleteListView = (id) => {
  return request.delete(`/api/list-views/${id}`)
}

// 获取列表数据
export const getListData = (id, params = {}) => {
  return request.get(`/api/list-views/${id}/data`, { params })
}

// ==================== 统计分析 API ====================

// 获取系统概览统计
export const getSystemOverview = () => {
  return request.get('/api/statistics/overview')
}

// 获取表单提交统计
export const getFormSubmissionStats = (params = {}) => {
  return request.get('/api/statistics/form-submissions', { params })
}

// 获取字段值统计
export const getFieldValueStats = (params = {}) => {
  return request.get('/api/statistics/field-values', { params })
}

// 获取表单模板统计概览
export const getFormTemplateOverview = (params = {}) => {
  return request.get('/api/statistics/form-templates', { params })
}

// 获取用户活跃度统计
export const getUserActivityStats = (params = {}) => {
  return request.get('/api/statistics/user-activity', { params })
}

// 获取字典使用统计
export const getDictionaryUsageStats = () => {
  return request.get('/api/statistics/dictionary-usage')
}

// ==================== 工作流 API ====================

// 获取工作流列表
export const getWorkflows = (params = {}) => {
  return request.get('/api/workflows', { params })
}

// 获取单个工作流详情
export const getWorkflow = (id) => {
  return request.get(`/api/workflows/${id}`)
}

// 创建工作流
export const createWorkflow = (data) => {
  return request.post('/api/workflows', data)
}

// 更新工作流
export const updateWorkflow = (id, data) => {
  return request.put(`/api/workflows/${id}`, data)
}

// 删除工作流
export const deleteWorkflow = (id) => {
  return request.delete(`/api/workflows/${id}`)
}

// 启动工作流实例
export const startWorkflowInstance = (id, data) => {
  return request.post(`/api/workflows/${id}/start`, data)
}

// 获取工作流实例列表
export const getWorkflowInstances = (id, params = {}) => {
  return request.get(`/api/workflows/${id}/instances`, { params })
}
