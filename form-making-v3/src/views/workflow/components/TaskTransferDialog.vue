<template>
  <el-dialog
    v-model="dialogVisible"
    title="转派任务"
    width="500px"
    :before-close="handleClose"
  >
    <div class="task-transfer-form" v-if="task">
      <!-- 任务信息 -->
      <div class="task-info">
        <h4>{{ task.title }}</h4>
        <p class="task-meta">{{ task.workflow_name }} - {{ task.step_name }}</p>
        <p class="current-assignee">当前处理人：{{ task.assignee }}</p>
      </div>

      <!-- 转派表单 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="转派给" prop="newAssignee">
          <el-select v-model="form.newAssignee" placeholder="选择新的处理人" style="width: 100%">
            <el-option label="张财务" value="zhang_finance" />
            <el-option label="李经理" value="li_manager" />
            <el-option label="王总" value="wang_ceo" />
            <el-option label="赵主管" value="zhao_manager" />
            <el-option label="刘专员" value="liu_specialist" />
          </el-select>
        </el-form-item>

        <el-form-item label="转派原因" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入转派原因..."
          />
        </el-form-item>

        <el-form-item label="是否通知">
          <el-switch
            v-model="form.notify"
            active-text="发送通知"
            inactive-text="不发送"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确认转派
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'transferred'])

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref(null)

const form = reactive({
  newAssignee: '',
  reason: '',
  notify: true
})

const rules = {
  newAssignee: [
    { required: true, message: '请选择新的处理人', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入转派原因', trigger: 'blur' }
  ]
}

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
  }
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    newAssignee: '',
    reason: '',
    notify: true
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('transferred', {
      taskId: props.task.id,
      ...form
    })
    
    dialogVisible.value = false
  } catch (error) {
    console.error('转派任务失败:', error)
    ElMessage.error('转派任务失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.task-info {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: 0.5rem;

  h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
  }

  .task-meta {
    margin: 0 0 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }

  .current-assignee {
    margin: 0;
    color: var(--text-light);
    font-size: 0.875rem;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
</style>
