<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`分类字典 - ${category?.name || ''}`"
    width="900px"
    :before-close="handleClose"
    class="vuexy-dialog"
  >
    <div v-if="category" class="category-dictionaries">
      <!-- 分类信息 -->
      <div class="category-info-card">
        <div class="category-header">
          <div class="category-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="category-details">
            <h3>{{ category.name }}</h3>
            <p class="category-code">{{ category.code }}</p>
            <p class="category-description">{{ category.description || '暂无描述' }}</p>
          </div>
          <div class="category-stats">
            <div class="stat-item">
              <div class="stat-number">{{ dictionaryList.length }}</div>
              <div class="stat-label">字典数量</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作栏 -->
      <div class="action-bar">
        <div class="action-left">
          <div class="vuexy-search-box">
            <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
              <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
              <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索字典名称或编码"
              class="search-input"
            />
          </div>
        </div>
        <div class="action-right">
          <button @click="refreshDictionaries" class="action-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
              <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
            </svg>
            刷新
          </button>
          <button @click="createDictionary" class="action-btn primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
              <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
            </svg>
            创建字典
          </button>
        </div>
      </div>

      <!-- 字典列表 -->
      <div class="dictionaries-table">
        <el-table
          :data="filteredDictionaries"
          v-loading="loading"
          style="width: 100%"
          max-height="400"
        >
          <el-table-column label="字典信息" min-width="200">
            <template #default="{ row }">
              <div class="dictionary-info">
                <div class="dictionary-name">{{ row.name }}</div>
                <div class="dictionary-code">{{ row.code }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="描述" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.description || '暂无描述' }}
            </template>
          </el-table-column>

          <el-table-column label="项目数" width="80" align="center">
            <template #default="{ row }">
              <span class="item-count">{{ row.item_count || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <div class="vuexy-status-tag" :class="getVuexyStatusClass(row.status)">
                <div class="status-dot"></div>
                {{ getStatusName(row.status) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="更新时间" width="120">
            <template #default="{ row }">
              <span class="update-time">{{ formatDate(row.updated_at) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-link type="primary" @click="editDictionary(row)">编辑</el-link>
                <el-link type="info" @click="viewDictionary(row)">查看</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && dictionaryList.length === 0" class="empty-state">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="empty-icon">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
          <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
          <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
          <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
          <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
        </svg>
        <h3>暂无字典</h3>
        <p>该分类下还没有字典，点击上方按钮创建第一个字典</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <button class="action-btn" @click="handleClose">
          关闭
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { getDictionaries } from '@/util/api'

const router = useRouter()

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  category: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const dictionaryList = ref([])
const searchQuery = ref('')

// 计算属性
const filteredDictionaries = computed(() => {
  if (!searchQuery.value) return dictionaryList.value
  
  const query = searchQuery.value.toLowerCase()
  return dictionaryList.value.filter(dict =>
    dict.name.toLowerCase().includes(query) ||
    dict.code.toLowerCase().includes(query)
  )
})

// 方法
const loadDictionaries = async () => {
  if (!props.category?.id) return
  
  try {
    loading.value = true
    const response = await getDictionaries({
      category_id: props.category.id,
      limit: 1000 // 获取所有字典
    })
    
    if (response.success) {
      dictionaryList.value = response.data.list || []
    }
  } catch (error) {
    console.error('加载分类字典失败:', error)
    ElMessage.error('加载分类字典失败')
  } finally {
    loading.value = false
  }
}

const refreshDictionaries = () => {
  loadDictionaries()
}

const createDictionary = () => {
  // 跳转到字典管理页面并预设分类
  router.push({
    path: '/dictionary/management',
    query: { 
      action: 'create',
      category_id: props.category.id 
    }
  })
  handleClose()
}

const editDictionary = (dictionary) => {
  // 跳转到字典管理页面编辑
  router.push({
    path: '/dictionary/management',
    query: { 
      action: 'edit',
      id: dictionary.id 
    }
  })
  handleClose()
}

const viewDictionary = (dictionary) => {
  // 跳转到字典管理页面查看
  router.push({
    path: '/dictionary/management',
    query: { 
      action: 'view',
      id: dictionary.id 
    }
  })
  handleClose()
}

const getVuexyStatusClass = (status) => {
  const statusMap = {
    active: 'status-success',
    inactive: 'status-secondary',
    draft: 'status-warning'
  }
  return statusMap[status] || 'status-secondary'
}

const getStatusName = (status) => {
  const statusMap = {
    active: '已启用',
    inactive: '已停用',
    draft: '草稿'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.category) {
    loadDictionaries()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style lang="scss" scoped>
.category-dictionaries {
  .category-info-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .category-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }

  .category-icon {
    width: 48px;
    height: 48px;
    background: rgba(115, 103, 240, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
  }

  .category-details {
    flex: 1;
    
    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-color-primary);
    }
    
    .category-code {
      font-size: 0.875rem;
      color: var(--text-color-secondary);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      margin: 0 0 0.5rem 0;
    }
    
    .category-description {
      font-size: 0.875rem;
      color: var(--text-color-secondary);
      margin: 0;
      line-height: 1.5;
    }
  }

  .category-stats {
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--primary-color);
        line-height: 1;
      }
      
      .stat-label {
        font-size: 0.75rem;
        color: var(--text-color-secondary);
        margin-top: 0.25rem;
      }
    }
  }

  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
  }

  .action-left {
    flex: 1;
    max-width: 300px;
  }

  .action-right {
    display: flex;
    gap: 0.75rem;
  }

  .dictionaries-table {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
  }

  .dictionary-info {
    .dictionary-name {
      font-weight: 600;
      color: var(--text-color-primary);
      margin-bottom: 0.25rem;
    }
    
    .dictionary-code {
      font-size: 0.75rem;
      color: var(--text-color-secondary);
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }

  .item-count {
    font-weight: 600;
    color: var(--primary-color);
  }

  .update-time {
    font-size: 0.75rem;
    color: var(--text-color-secondary);
  }

  .table-actions {
    display: flex;
    gap: 0.5rem;
  }

  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    
    .empty-icon {
      color: var(--text-color-secondary);
      margin-bottom: 1rem;
    }
    
    h3 {
      margin: 0 0 0.5rem 0;
      color: var(--text-color-primary);
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: var(--text-color-secondary);
      font-size: 0.875rem;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 复用样式 */
.vuexy-search-box {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  z-index: 2;
}

.search-input {
  width: 100%;
  height: 36px;
  padding: 0 16px 0 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 36px;
  padding: 0 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.vuexy-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.status-success {
  background: rgba(40, 199, 111, 0.1);
  color: #28c76f;
}

.status-warning {
  background: rgba(255, 159, 67, 0.1);
  color: #ff9f43;
}

.status-secondary {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}
</style>
