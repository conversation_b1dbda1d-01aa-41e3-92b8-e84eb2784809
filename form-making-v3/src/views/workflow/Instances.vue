<template>
  <div class="vuexy-workflow-instances">
    <!-- Vuexy 风格筛选区域 -->
    <div class="vuexy-filter-section">
      <div class="filter-left">
        <div class="vuexy-search-box">
          <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索流程实例标题或发起人"
            class="search-input"
          />
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedStatus" class="vuexy-select">
            <option value="">全部状态</option>
            <option value="running">运行中</option>
            <option value="completed">已完成</option>
            <option value="suspended">已暂停</option>
            <option value="terminated">已终止</option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedWorkflow" class="vuexy-select">
            <option value="">全部流程</option>
            <option value="leave">请假申请</option>
            <option value="purchase">采购申请</option>
            <option value="expense">报销申请</option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
      <div class="filter-right">
        <button @click="refreshInstances" class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
          刷新
        </button>
        <button @click="startInstanceDialogVisible = true" class="action-btn primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
          </svg>
          启动流程
        </button>
      </div>
    </div>

    <!-- 实例统计卡片 -->
    <div class="instance-stats-grid">
      <div class="stat-card running">
        <div class="stat-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ instanceStats.running }}</div>
          <div class="stat-label">运行中</div>
        </div>
      </div>
      <div class="stat-card completed">
        <div class="stat-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <polyline points="9,12 12,15 16,11" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ instanceStats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      <div class="stat-card suspended">
        <div class="stat-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <rect x="9" y="9" width="6" height="6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ instanceStats.suspended }}</div>
          <div class="stat-label">已暂停</div>
        </div>
      </div>
      <div class="stat-card terminated">
        <div class="stat-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
            <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ instanceStats.terminated }}</div>
          <div class="stat-label">已终止</div>
        </div>
      </div>
    </div>
    <!-- 改进的实例表格 -->
    <div class="vuexy-table" v-loading="loading">
      <el-table
        :data="filteredInstances"
        style="width: 100%"
        :stripe="false"
        :border="false"
        :highlight-current-row="true"
        @current-change="handleCurrentRow"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="流程信息" min-width="300">
          <template #default="scope">
            <div class="product-info">
              <div class="product-avatar">
                <div class="instance-status-indicator" :class="getStatusClass(scope.row.status)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                    <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
              </div>
              <div class="product-details">
                <div class="product-name">{{ scope.row.title }}</div>
                <div class="product-description">{{ scope.row.workflow_name }} - {{ scope.row.current_step }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="实例ID" width="120">
          <template #default="scope">
            <span class="instance-id">{{ scope.row.id }}</span>
          </template>
        </el-table-column>

        <el-table-column label="发起人" width="120">
          <template #default="scope">
            <div class="user-info">
              <div class="user-avatar">{{ scope.row.initiator.charAt(0) }}</div>
              <span class="user-name">{{ scope.row.initiator }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120">
          <template #default="scope">
            <div class="vuexy-status-tag" :class="getStatusClass(scope.row.status)">
              <div class="status-dot"></div>
              {{ getStatusName(scope.row.status) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="进度" width="150">
          <template #default="scope">
            <div class="progress-info">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: scope.row.progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ scope.row.progress }}%</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="150">
          <template #default="scope">
            <span class="create-time">{{ formatDate(scope.row.created_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="更新时间" width="150">
          <template #default="scope">
            <span class="create-time">{{ formatDate(scope.row.updated_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="vuexy-table-actions">
              <el-button
                size="small"
                @click="viewInstanceDetail(scope.row)"
                title="查看详情"
              >
                详情
              </el-button>
              <el-button
                v-if="scope.row.status === 'running'"
                type="warning"
                size="small"
                @click="suspendInstance(scope.row)"
                title="暂停实例"
              >
                暂停
              </el-button>
              <el-button
                v-if="scope.row.status === 'suspended'"
                type="primary"
                size="small"
                @click="resumeInstance(scope.row)"
                title="恢复实例"
              >
                恢复
              </el-button>
              <el-button
                v-if="scope.row.status === 'running' || scope.row.status === 'suspended'"
                type="danger"
                size="small"
                @click="terminateInstance(scope.row)"
                title="终止实例"
              >
                终止
              </el-button>
              <el-dropdown @command="handleInstanceAction" trigger="click">
                <el-button size="small" title="更多操作">
                  更多
                  <el-icon class="el-icon--right">
                    <ArrowDown />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`history-${scope.row.id}`">查看历史</el-dropdown-item>
                    <el-dropdown-item :command="`diagram-${scope.row.id}`">流程图</el-dropdown-item>
                    <el-dropdown-item :command="`export-${scope.row.id}`" divided>导出数据</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 改进的分页器 -->
      <div class="vuexy-pagination" v-if="totalInstances > 0">
        <div class="pagination-info">
          显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalInstances) }} 条，共 {{ totalInstances }} 条记录
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalInstances"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 启动流程对话框 -->
    <el-dialog
      v-model="startInstanceDialogVisible"
      title="启动流程"
      width="600px"
    >
      <div class="start-instance-form">
        <el-form label-width="100px">
          <el-form-item label="选择流程">
            <el-select placeholder="请选择要启动的流程" style="width: 100%">
              <el-option label="请假申请流程" value="leave" />
              <el-option label="采购申请流程" value="purchase" />
              <el-option label="报销申请流程" value="expense" />
            </el-select>
          </el-form-item>
          <el-form-item label="流程标题">
            <el-input placeholder="请输入流程实例标题" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input type="textarea" :rows="3" placeholder="请输入流程描述" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="startInstanceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleStartInstance">启动</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>



<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const instances = ref([])
const searchQuery = ref('')
const selectedStatus = ref('')
const selectedWorkflow = ref('')
const startInstanceDialogVisible = ref(false)
const instanceDetailVisible = ref(false)
const selectedInstanceId = ref(null)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const totalInstances = ref(0)

// 实例统计
const instanceStats = ref({
  running: 0,
  completed: 0,
  suspended: 0,
  terminated: 0
})

// 计算属性
const filteredInstances = computed(() => {
  let filtered = instances.value || []

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(instance =>
      instance.title.toLowerCase().includes(query) ||
      instance.workflow_name.toLowerCase().includes(query) ||
      instance.initiator.toLowerCase().includes(query)
    )
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(instance => instance.status === selectedStatus.value)
  }

  if (selectedWorkflow.value) {
    filtered = filtered.filter(instance => instance.workflow_type === selectedWorkflow.value)
  }

  return filtered
})

// 方法
const loadInstances = async () => {
  try {
    loading.value = true

    // 模拟API调用
    const mockData = {
      list: [
        {
          id: 'WF001',
          title: '张三的请假申请',
          workflow_name: '请假申请流程',
          workflow_type: 'leave',
          current_step: '部门主管审批',
          status: 'running',
          progress: 60,
          initiator: '张三',
          created_at: '2024-01-15 09:30:00',
          updated_at: '2024-01-15 10:15:00'
        },
        {
          id: 'WF002',
          title: '李四的采购申请',
          workflow_name: '采购申请流程',
          workflow_type: 'purchase',
          current_step: '财务审批',
          status: 'running',
          progress: 40,
          initiator: '李四',
          created_at: '2024-01-15 08:45:00',
          updated_at: '2024-01-15 09:20:00'
        },
        {
          id: 'WF003',
          title: '王五的报销申请',
          workflow_name: '报销申请流程',
          workflow_type: 'expense',
          current_step: '已完成',
          status: 'completed',
          progress: 100,
          initiator: '王五',
          created_at: '2024-01-14 14:20:00',
          updated_at: '2024-01-15 11:30:00'
        },
        {
          id: 'WF004',
          title: '赵六的设备申请',
          workflow_name: '设备申请流程',
          workflow_type: 'equipment',
          current_step: '已暂停',
          status: 'suspended',
          progress: 25,
          initiator: '赵六',
          created_at: '2024-01-12 10:15:00',
          updated_at: '2024-01-13 16:30:00'
        }
      ],
      total: 4,
      stats: {
        running: 2,
        completed: 1,
        suspended: 1,
        terminated: 0
      }
    }

    instances.value = mockData.list
    totalInstances.value = mockData.total
    instanceStats.value = mockData.stats

  } catch (error) {
    console.error('加载流程实例失败:', error)
    ElMessage.error('加载流程实例失败')
  } finally {
    loading.value = false
  }
}

const refreshInstances = () => {
  currentPage.value = 1
  loadInstances()
}

// 实例操作方法
const viewInstanceDetail = (instance) => {
  selectedInstanceId.value = instance.id
  instanceDetailVisible.value = true
}

const suspendInstance = async (instance) => {
  try {
    await ElMessageBox.confirm(`确定要暂停实例 ${instance.id} 吗？`, '确认暂停', {
      type: 'warning'
    })

    // 模拟API调用
    ElMessage.success('实例已暂停')
    loadInstances()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('暂停实例失败')
    }
  }
}

const resumeInstance = async (instance) => {
  try {
    // 模拟API调用
    ElMessage.success('实例已恢复')
    loadInstances()
  } catch (error) {
    ElMessage.error('恢复实例失败')
  }
}

const terminateInstance = async (instance) => {
  try {
    await ElMessageBox.confirm(`确定要终止实例 ${instance.id} 吗？此操作不可撤销！`, '确认终止', {
      type: 'warning'
    })

    // 模拟API调用
    ElMessage.success('实例已终止')
    loadInstances()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('终止实例失败')
    }
  }
}

// 表格事件处理
const handleCurrentRow = (row) => {
  // 处理当前行选择
}

const handleSelectionChange = (selection) => {
  // 处理选择变化
}

const handleInstanceAction = (command) => {
  const [action, id] = command.split('-')
  const instance = instances.value.find(i => i.id === id)

  switch (action) {
    case 'history':
      viewInstanceHistory(instance)
      break
    case 'diagram':
      viewInstanceDiagram(instance)
      break
    case 'export':
      exportInstanceData(instance)
      break
  }
}

const viewInstanceHistory = (instance) => {
  ElMessage.info(`查看实例 ${instance.id} 的历史记录`)
}

const viewInstanceDiagram = (instance) => {
  ElMessage.info(`查看实例 ${instance.id} 的流程图`)
}

const exportInstanceData = (instance) => {
  ElMessage.info(`导出实例 ${instance.id} 的数据`)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadInstances()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadInstances()
}
// 工具方法
const getStatusClass = (status) => {
  const statusMap = {
    running: 'status-info',
    completed: 'status-success',
    suspended: 'status-warning',
    terminated: 'status-danger'
  }
  return statusMap[status] || 'status-info'
}

const getStatusName = (status) => {
  const statusMap = {
    running: '运行中',
    completed: '已完成',
    suspended: '已暂停',
    terminated: '已终止'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
// 组件挂载时加载数据
// 启动流程处理
const handleStartInstance = () => {
  ElMessage.success('流程启动成功')
  startInstanceDialogVisible.value = false
  loadInstances()
}

// 组件挂载时加载数据
onMounted(() => {
  loadInstances()
})
</script>

<style lang="scss" scoped>
.vuexy-workflow-instances {
  padding: 1.5rem;
  background: var(--background-color);
  min-height: 100vh;
}

// 筛选区域样式
.vuexy-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);

  .filter-left {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex: 1;
  }

  .filter-right {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
}

.vuexy-search-box {
  position: relative;
  min-width: 300px;

  .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    z-index: 1;
  }

  .search-input {
    background: var(--input-background);
    border: 2px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem 1rem 0.75rem 3rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    width: 300px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    &::placeholder {
      color: var(--text-light);
    }

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
    }

    &:hover {
      border-color: var(--text-light);
    }
  }
}

.vuexy-select-wrapper {
  position: relative;
  min-width: 150px;
  display: inline-block;

  .vuexy-select {
    background: var(--input-background);
    border: 2px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 120px;
    width: 100%;
    appearance: none;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
    }

    &:hover {
      border-color: var(--text-light);
    }
  }

  .select-arrow {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    pointer-events: none;
    z-index: 2;
    width: 16px;
    height: 16px;
  }

  &:hover .select-arrow {
    color: var(--text-primary);
  }
}

// 实例统计卡片样式
.instance-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
  }

  .stat-content {
    flex: 1;

    .stat-number {
      font-size: 1.75rem;
      font-weight: 600;
      line-height: 1;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--text-secondary);
    }
  }

  &.running {
    .stat-icon {
      background: rgba(0, 207, 232, 0.1);
      color: #00cfe8;
    }
    .stat-number {
      color: #00cfe8;
    }
  }

  &.completed {
    .stat-icon {
      background: rgba(40, 199, 111, 0.1);
      color: #28c76f;
    }
    .stat-number {
      color: #28c76f;
    }
  }

  &.suspended {
    .stat-icon {
      background: rgba(255, 159, 67, 0.1);
      color: #ff9f43;
    }
    .stat-number {
      color: #ff9f43;
    }
  }

  &.terminated {
    .stat-icon {
      background: rgba(234, 84, 85, 0.1);
      color: #ea5455;
    }
    .stat-number {
      color: #ea5455;
    }
  }
}

// 表格样式
.vuexy-table {
  background: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);
  overflow: hidden;

  :deep(.el-table) {
    background: transparent;

    .el-table__header {
      background: var(--table-header-background);

      th {
        background: transparent;
        border: none;
        color: var(--text-primary);
        font-weight: 600;
        font-size: 0.875rem;
        padding: 1rem 0.75rem;
      }
    }

    .el-table__body {
      tr {
        background: transparent;

        &:hover {
          background: var(--hover-background);
        }

        td {
          border: none;
          padding: 1rem 0.75rem;
          color: var(--text-primary);
        }
      }
    }
  }
}

// 产品信息样式
.product-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  .product-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-rgba);
    color: var(--primary-color);
    flex-shrink: 0;

    .instance-status-indicator {
      width: 1.5rem;
      height: 1.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.status-info {
        background: rgba(0, 207, 232, 0.1);
        color: #00cfe8;
      }

      &.status-success {
        background: rgba(40, 199, 111, 0.1);
        color: #28c76f;
      }

      &.status-warning {
        background: rgba(255, 159, 67, 0.1);
        color: #ff9f43;
      }

      &.status-danger {
        background: rgba(234, 84, 85, 0.1);
        color: #ea5455;
      }
    }
  }

  .product-details {
    flex: 1;
    min-width: 0;

    .product-name {
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
    }

    .product-description {
      color: var(--text-secondary);
      font-size: 0.75rem;
      line-height: 1.4;
    }
  }
}

// 用户信息样式
.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .user-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
  }

  .user-name {
    font-size: 0.875rem;
    color: var(--text-primary);
  }
}

// 状态标签样式
.vuexy-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;

  .status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: currentColor;
  }

  &.status-success {
    background: var(--success-light);
    color: var(--success-color);
  }

  &.status-warning {
    background: var(--warning-light);
    color: var(--warning-color);
  }

  &.status-info {
    background: var(--info-light);
    color: var(--info-color);
  }

  &.status-danger {
    background: var(--danger-light);
    color: var(--danger-color);
  }
}

// 进度条样式
.progress-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .progress-bar {
    flex: 1;
    height: 0.5rem;
    background: var(--border-light);
    border-radius: 0.25rem;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: var(--primary-color);
      border-radius: 0.25rem;
      transition: width 0.3s ease;
    }
  }

  .progress-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    min-width: 2.5rem;
    text-align: right;
  }
}

// 实例ID样式
.instance-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: var(--background-light);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

// 时间样式
.create-time {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

// 操作按钮样式
.vuexy-table-actions {
  display: flex;
  gap: 0.375rem;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;

  :deep(.el-button) {
    margin: 0;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    height: auto;
    min-height: 1.75rem;
    border-radius: 0.25rem;

    &.el-button--small {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }

    &.el-button--primary {
      background: var(--primary-color);
      border-color: var(--primary-color);

      &:hover {
        background: var(--primary-dark);
        border-color: var(--primary-dark);
      }
    }

    &.el-button--warning {
      background: var(--warning-color);
      border-color: var(--warning-color);

      &:hover {
        background: var(--warning-dark);
        border-color: var(--warning-dark);
      }
    }

    &.el-button--danger {
      background: var(--danger-color);
      border-color: var(--danger-color);

      &:hover {
        background: var(--danger-dark);
        border-color: var(--danger-dark);
      }
    }

    &.el-button--default {
      background: var(--card-background);
      border-color: var(--border-color);
      color: var(--text-primary);

      &:hover {
        background: var(--hover-background);
        border-color: var(--text-light);
        color: var(--text-primary);
      }
    }
  }

  .el-dropdown {
    :deep(.el-button) {
      background: var(--card-background);
      border-color: var(--border-color);
      color: var(--text-primary);

      &:hover {
        background: var(--hover-background);
        border-color: var(--text-light);
        color: var(--text-primary);
      }
    }
  }
}

// 分页器样式
.vuexy-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-top: 1px solid var(--border-light);

  .pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
  }

  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump {
      color: var(--text-secondary);
    }

    .el-pager li {
      background: transparent;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);
      margin: 0 0.25rem;

      &.is-active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }

      &:hover:not(.is-active) {
        background: var(--hover-background);
        color: var(--text-primary);
      }
    }

    .btn-prev,
    .btn-next {
      background: transparent;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);

      &:hover {
        background: var(--hover-background);
        color: var(--text-primary);
      }
    }
  }
}

// 按钮样式
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background: var(--card-background);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;

  &:hover {
    background: var(--hover-background);
    border-color: var(--text-light);
    transform: translateY(-1px);
  }

  &.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);

    &:hover {
      background: var(--primary-dark);
      border-color: var(--primary-dark);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .vuexy-workflow-instances {
    padding: 1rem;
  }

  .vuexy-filter-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .filter-left {
      flex-direction: column;
      gap: 0.75rem;
    }

    .filter-right {
      justify-content: center;
    }
  }

  .vuexy-search-box {
    min-width: auto;

    .search-input {
      width: 100%;
    }
  }

  .instance-stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .vuexy-table-actions {
    flex-wrap: nowrap;
    gap: 0.25rem;
    overflow: hidden;

    :deep(.el-button) {
      padding: 0.125rem 0.375rem;
      font-size: 0.625rem;
      min-height: 1.5rem;
    }
  }

  .vuexy-pagination {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
</style>
