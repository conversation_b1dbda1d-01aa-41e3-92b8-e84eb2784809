<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务历史"
    width="700px"
    :before-close="handleClose"
  >
    <div class="task-history" v-if="task">
      <!-- 任务信息 -->
      <div class="task-info">
        <h4>{{ task.title }}</h4>
        <p class="task-meta">{{ task.workflow_name }}</p>
      </div>

      <!-- 历史记录 -->
      <div class="history-timeline">
        <div class="timeline-item" v-for="(item, index) in historyData" :key="index">
          <div class="timeline-dot" :class="item.status"></div>
          <div class="timeline-content">
            <div class="timeline-header">
              <div class="timeline-title">{{ item.title }}</div>
              <div class="timeline-time">{{ item.time }}</div>
            </div>
            <div class="timeline-user">{{ item.user }}</div>
            <div class="timeline-comment" v-if="item.comment">{{ item.comment }}</div>
            <div class="timeline-result" v-if="item.result">
              <span class="result-label" :class="item.result.type">{{ item.result.text }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = ref(false)

// 模拟历史数据
const historyData = computed(() => [
  {
    title: '任务创建',
    user: '张三',
    time: '2024-01-15 09:30:00',
    comment: '提交请假申请，申请年假3天',
    status: 'completed',
    result: null
  },
  {
    title: '自动分配',
    user: '系统',
    time: '2024-01-15 09:31:00',
    comment: '自动分配给部门主管进行审批',
    status: 'completed',
    result: null
  },
  {
    title: '部门主管审批',
    user: '李主管',
    time: '2024-01-15 14:20:00',
    comment: '同意申请，员工表现良好，同意年假申请',
    status: 'completed',
    result: {
      type: 'approve',
      text: '同意'
    }
  },
  {
    title: '转派任务',
    user: '李主管',
    time: '2024-01-15 14:22:00',
    comment: '转派给HR进行最终确认',
    status: 'completed',
    result: null
  },
  {
    title: 'HR确认',
    user: '王HR',
    time: '待处理',
    comment: '等待HR最终确认',
    status: 'current',
    result: null
  }
])

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.task-history {
  max-height: 70vh;
  overflow-y: auto;
}

.task-info {
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: 0.5rem;

  h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
  }

  .task-meta {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }
}

.history-timeline {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
  }

  .timeline-item {
    position: relative;
    padding-left: 3rem;
    margin-bottom: 2rem;

    &:last-child {
      margin-bottom: 0;
    }

    .timeline-dot {
      position: absolute;
      left: 0.75rem;
      top: 0.5rem;
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
      border: 2px solid var(--border-color);
      background: var(--card-background);
      z-index: 1;

      &.completed {
        background: var(--success-color);
        border-color: var(--success-color);
      }

      &.current {
        background: var(--primary-color);
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px rgba(115, 103, 240, 0.2);
      }

      &.pending {
        background: var(--card-background);
        border-color: var(--border-color);
      }
    }

    .timeline-content {
      background: var(--card-background);
      border: 1px solid var(--border-light);
      border-radius: 0.5rem;
      padding: 1rem;

      .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .timeline-title {
          font-weight: 600;
          color: var(--text-primary);
          font-size: 0.95rem;
        }

        .timeline-time {
          font-size: 0.875rem;
          color: var(--text-secondary);
        }
      }

      .timeline-user {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
      }

      .timeline-comment {
        font-size: 0.875rem;
        color: var(--text-primary);
        line-height: 1.5;
        margin-bottom: 0.5rem;
      }

      .timeline-result {
        .result-label {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.75rem;
          border-radius: 1rem;
          font-size: 0.75rem;
          font-weight: 500;

          &.approve {
            background: var(--success-light);
            color: var(--success-color);
          }

          &.reject {
            background: var(--danger-light);
            color: var(--danger-color);
          }

          &.return {
            background: var(--warning-light);
            color: var(--warning-color);
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
