const mysql = require('mysql2/promise');
require('dotenv').config();

// 创建数据库连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'test',
  database: process.env.DB_NAME || 'form_designer',
  charset: 'utf8mb4',
  timezone: '+08:00',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
});

// 测试数据库连接
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
};

// 数据库工具类
class Database {
  constructor() {
    this.pool = pool;
  }

  // 执行查询
  async query(sql, params = []) {
    let connection;
    try {
      connection = await this.pool.getConnection();
      const [rows, fields] = await connection.query(sql, params);
      return { rows, fields };
    } catch (error) {
      console.error('数据库查询失败:', error);
      throw error;
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }

  // 执行事务
  async transaction(callback) {
    let connection;
    try {
      connection = await this.pool.getConnection();
      await connection.beginTransaction();

      const result = await callback(connection);

      await connection.commit();
      return result;
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      throw error;
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }

  // 分页查询
  async paginate(sql, params = [], page = 1, pageSize = 10) {
    const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_table`;
    const { rows: countRows } = await this.query(countSql, params);
    const total = countRows[0].total;

    const offset = (page - 1) * pageSize;
    const paginatedSql = `${sql} LIMIT ? OFFSET ?`;
    const paginatedParams = [...params, pageSize, offset];

    const { rows } = await this.query(paginatedSql, paginatedParams);

    return {
      data: rows,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total: parseInt(total),
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }
}

// 初始化数据库表
const initDatabase = async () => {
  try {
    // 创建表单模板表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS form_templates (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(255) NOT NULL COMMENT '表单名称',
        description TEXT COMMENT '表单描述',
        form_config JSON NOT NULL COMMENT '表单配置JSON',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100) COMMENT '创建者',
        status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
        INDEX idx_name (name),
        INDEX idx_created_at (created_at),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建表单提交数据表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS form_submissions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        template_id INT NOT NULL,
        form_data JSON NOT NULL COMMENT '表单填写数据',
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        submitted_by VARCHAR(100) COMMENT '提交者',
        INDEX idx_template_id (template_id),
        INDEX idx_submitted_at (submitted_at),
        FOREIGN KEY (template_id) REFERENCES form_templates(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建字典分类表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS dictionary_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT '分类名称',
        code VARCHAR(50) NOT NULL UNIQUE COMMENT '分类编码',
        description TEXT COMMENT '分类描述',
        parent_id INT DEFAULT NULL COMMENT '父分类ID',
        sort_order INT DEFAULT 0 COMMENT '排序',
        status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100) DEFAULT 'system',
        INDEX idx_parent_id (parent_id),
        INDEX idx_code (code),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典分类表'
    `);

    // 创建字典表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS dictionaries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT '字典名称',
        code VARCHAR(50) NOT NULL COMMENT '字典编码',
        category_id INT NOT NULL COMMENT '分类ID',
        description TEXT COMMENT '字典描述',
        data_type ENUM('string', 'number', 'boolean', 'object') DEFAULT 'string' COMMENT '数据类型',
        version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
        status ENUM('active', 'inactive', 'draft') DEFAULT 'draft' COMMENT '状态',
        is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统字典',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by VARCHAR(100) DEFAULT 'system',
        UNIQUE KEY uk_category_code (category_id, code),
        INDEX idx_category_id (category_id),
        INDEX idx_code (code),
        INDEX idx_status (status),
        FOREIGN KEY (category_id) REFERENCES dictionary_categories(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典表'
    `);

    // 创建字典项表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS dictionary_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        dictionary_id INT NOT NULL COMMENT '字典ID',
        label VARCHAR(200) NOT NULL COMMENT '显示标签',
        value VARCHAR(500) NOT NULL COMMENT '字典值',
        description TEXT COMMENT '描述',
        sort_order INT DEFAULT 0 COMMENT '排序',
        parent_id INT DEFAULT NULL COMMENT '父项ID（用于树形结构）',
        extra_data JSON COMMENT '扩展数据',
        status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_dictionary_id (dictionary_id),
        INDEX idx_parent_id (parent_id),
        INDEX idx_value (value),
        INDEX idx_status (status),
        INDEX idx_sort_order (sort_order),
        FOREIGN KEY (dictionary_id) REFERENCES dictionaries(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典项表'
    `);

    // 创建字典版本历史表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS dictionary_versions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        dictionary_id INT NOT NULL COMMENT '字典ID',
        version VARCHAR(20) NOT NULL COMMENT '版本号',
        items_data JSON NOT NULL COMMENT '字典项数据快照',
        change_log TEXT COMMENT '变更日志',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(100) DEFAULT 'system',
        INDEX idx_dictionary_id (dictionary_id),
        INDEX idx_version (version),
        FOREIGN KEY (dictionary_id) REFERENCES dictionaries(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典版本历史表'
    `);

    // 创建字典使用记录表
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS dictionary_usage (
        id INT AUTO_INCREMENT PRIMARY KEY,
        dictionary_id INT NOT NULL COMMENT '字典ID',
        form_template_id INT NOT NULL COMMENT '表单模板ID',
        field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
        usage_type ENUM('dropdown', 'radio', 'checkbox', 'autocomplete') NOT NULL COMMENT '使用类型',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_dictionary_id (dictionary_id),
        INDEX idx_form_template_id (form_template_id),
        FOREIGN KEY (dictionary_id) REFERENCES dictionaries(id) ON DELETE CASCADE,
        FOREIGN KEY (form_template_id) REFERENCES form_templates(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典使用记录表'
    `);

    console.log('✅ 表单和字典相关表初始化成功');
  } catch (error) {
    console.error('❌ 表单和字典相关表初始化失败:', error.message);
    throw error;
  }
};

// 初始化认证系统表
const initAuthTables = async () => {
  const fs = require('fs');
  const path = require('path');

  try {
    // 读取认证表SQL文件
    const sqlPath = path.join(__dirname, '../../sql/auth_tables.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // 分割SQL语句并执行
    const statements = sql.split(';').filter(stmt => stmt.trim());

    for (const statement of statements) {
      if (statement.trim()) {
        await pool.execute(statement);
      }
    }

    console.log('✅ 认证系统表初始化成功');
  } catch (error) {
    console.error('❌ 认证系统表初始化失败:', error.message);
    throw error;
  }
};

// 初始化认证系统基础数据
const initAuthData = async () => {
  const fs = require('fs');
  const path = require('path');
  const bcrypt = require('bcryptjs');

  try {
    // 检查是否已有用户数据
    const [rows] = await pool.execute('SELECT COUNT(*) as count FROM users');
    if (rows[0].count > 0) {
      console.log('✅ 认证系统数据已存在，跳过初始化');
      return;
    }

    // 读取初始数据SQL文件
    const sqlPath = path.join(__dirname, '../../sql/auth_init_data.sql');
    let sql = fs.readFileSync(sqlPath, 'utf8');

    // 生成管理员密码哈希
    const adminPassword = 'admin123';
    const passwordHash = await bcrypt.hash(adminPassword, 10);

    // 替换占位符密码哈希
    sql = sql.replace('$2b$10$placeholder_hash_will_be_replaced', passwordHash);

    // 分割SQL语句并执行
    const statements = sql.split(';').filter(stmt => stmt.trim());

    for (const statement of statements) {
      if (statement.trim()) {
        await pool.execute(statement);
      }
    }

    console.log('✅ 认证系统基础数据初始化成功');
    console.log('📝 默认管理员账户: admin / admin123');
  } catch (error) {
    console.error('❌ 认证系统基础数据初始化失败:', error.message);
    throw error;
  }
};

// 初始化字典系统基础数据
const initDictionaryData = async () => {
  const fs = require('fs');
  const path = require('path');

  try {
    // 检查是否已有字典数据
    const [rows] = await pool.execute('SELECT COUNT(*) as count FROM dictionary_categories');
    if (rows[0].count > 0) {
      console.log('✅ 字典系统数据已存在，跳过初始化');
      return;
    }

    // 读取字典初始数据SQL文件
    const sqlPath = path.join(__dirname, '../../sql/dictionary_init_data.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // 分割SQL语句并执行
    const statements = sql.split(';').filter(stmt => stmt.trim());

    for (const statement of statements) {
      if (statement.trim()) {
        await pool.execute(statement);
      }
    }

    console.log('✅ 字典系统基础数据初始化成功');
    console.log('📚 已初始化医疗相关字典数据');
  } catch (error) {
    console.error('❌ 字典系统基础数据初始化失败:', error.message);
    throw error;
  }
};

// 创建数据库实例
const database = new Database();

module.exports = {
  pool,
  database,
  testConnection,
  initDatabase,
  initAuthTables,
  initAuthData,
  initDictionaryData
};
