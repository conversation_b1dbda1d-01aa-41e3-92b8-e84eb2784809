<template>
  <div class="vuexy-dictionary-management">
    <!-- 简化的筛选区域 -->
    <div class="filter-bar">
      <div class="filter-left">
        <h2 class="page-title">字典管理</h2>

        <div class="search-box">
          <svg class="search-icon" width="18" height="18" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索字典..."
            class="search-input"
          />
        </div>

        <select v-model="selectedCategory" class="filter-select">
          <option value="">全部分类</option>
          <option
            v-for="category in (categories || [])"
            :key="category.id"
            :value="category.id"
          >
            {{ category.name }}
          </option>
        </select>

        <select v-model="selectedStatus" class="filter-select">
          <option value="">全部状态</option>
          <option value="active">已启用</option>
          <option value="inactive">已停用</option>
          <option value="draft">草稿</option>
        </select>
      </div>
      <div class="filter-right">
        <button @click="viewMode = viewMode === 'table' ? 'card' : 'table'" class="view-toggle-btn">
          <svg v-if="viewMode === 'table'" width="16" height="16" viewBox="0 0 24 24" fill="none">
            <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
            <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
            <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
            <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
          </svg>
          <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
            <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2"/>
            <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2"/>
            <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2"/>
            <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <button @click="refreshDictionaries" class="action-btn" :disabled="loading">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <button @click="createDictionary" class="create-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
          </svg>
          创建字典
        </button>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-show="viewMode === 'table'" class="table-container">
      <div class="table-wrapper">
        <el-table
          :data="filteredDictionaries"
          v-loading="loading"
          :stripe="false"
          :border="false"
          style="width: 100%"
          class="modern-table"
          empty-text="暂无字典数据"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column label="字典名称" min-width="200">
            <template #default="{ row }">
              <div class="dict-name">
                <div class="name">{{ row.name }}</div>
                <div class="code">{{ row.code }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="分类" width="120">
            <template #default="{ row }">
              <span class="category">{{ row.category_name || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="描述" min-width="200">
            <template #default="{ row }">
              <span class="description">{{ row.description || '暂无描述' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="项目数" width="80">
            <template #default="{ row }">
              <span class="item-count">{{ row.item_count || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="table-actions">
                <el-button size="small" @click="editDictionary(row)">编辑</el-button>
                <el-button size="small" @click="viewDictionary(row)">查看</el-button>
                <el-button size="small" type="danger" @click="deleteDictionaryItem(row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 卡片视图 -->
    <div v-show="viewMode === 'card'" class="card-view">
      <div class="cards-grid">
        <div
          v-for="dictionary in filteredDictionaries"
          :key="dictionary.id"
          class="dictionary-card"
        >
          <div class="card-header">
            <div class="dict-info">
              <h3>{{ dictionary.name }}</h3>
              <div class="dict-code">{{ dictionary.code }}</div>
              <div v-if="dictionary.category_name" class="dict-category">
                {{ dictionary.category_name }}
              </div>
            </div>
            <div class="dict-status">
              <div class="vuexy-status-tag" :class="getVuexyStatusClass(dictionary.status)">
                <div class="status-dot"></div>
                {{ getStatusName(dictionary.status) }}
              </div>
            </div>
          </div>

          <div class="card-content">
            <div class="dict-description">
              {{ dictionary.description || '暂无描述' }}
            </div>

            <div class="dict-meta">
              <div class="meta-item">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <path d="M8 6h13M8 12h13M8 18h13M3 6h.01M3 12h.01M3 18h.01" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>{{ dictionary.item_count || 0 }} 项</span>
              </div>
              <div class="meta-item">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>{{ formatDate(dictionary.updated_at) }}</span>
              </div>
            </div>
          </div>

          <div class="card-actions">
            <div class="action-buttons">
              <button class="card-action-btn edit" @click="editDictionary(dictionary)" title="编辑">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="card-action-btn view" @click="viewDictionary(dictionary)" title="查看">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="card-action-btn copy" @click="copyDictionary(dictionary)" title="复制">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="card-action-btn delete" @click="deleteDictionaryItem(dictionary)" title="删除">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                  <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                  <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 分页 -->
    <div class="vuexy-pagination" v-if="totalDictionaries > 0">
      <div class="pagination-info">
        显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalDictionaries) }} 条，共 {{ totalDictionaries }} 条记录
      </div>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalDictionaries"
        layout="sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 分类管理对话框 -->
    <CategoryManagement 
      v-model:visible="manageCategoriesVisible"
      @refresh="loadCategories"
    />

    <!-- 字典编辑对话框 -->
    <DictionaryEditor
      v-model:visible="editorVisible"
      :dictionary="currentDictionary"
      :categories="categories"
      @saved="handleDictionarySaved"
    />

    <!-- 字典详情对话框 -->
    <DictionaryDetail
      v-model:visible="detailVisible"
      :dictionary="currentDictionary"
    />

    <!-- 使用情况对话框 -->
    <UsageDialog
      v-model:visible="usageVisible"
      :dictionary="currentDictionary"
    />

    <!-- 版本历史对话框 -->
    <VersionHistory
      v-model:visible="versionVisible"
      :dictionary="currentDictionary"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getDictionaries,
  getDictionaryCategories,
  deleteDictionary,
  updateDictionary
} from '@/util/api'
import CategoryManagement from './components/CategoryManagement.vue'
import DictionaryEditor from './components/DictionaryEditor.vue'
import DictionaryDetail from './components/DictionaryDetail.vue'
import UsageDialog from './components/UsageDialog.vue'
import VersionHistory from './components/VersionHistory.vue'

// 响应式数据
const loading = ref(false)
const dictionaries = ref([])
const categories = ref([])
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const viewMode = ref('table')
const currentPage = ref(1)
const pageSize = ref(20)
const totalDictionaries = ref(0)

// 对话框状态
const manageCategoriesVisible = ref(false)
const editorVisible = ref(false)
const detailVisible = ref(false)
const usageVisible = ref(false)
const versionVisible = ref(false)
const currentDictionary = ref(null)

// 计算属性
const filteredDictionaries = computed(() => {
  let filtered = dictionaries.value || []

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(dict =>
      dict.name.toLowerCase().includes(query) ||
      dict.code.toLowerCase().includes(query) ||
      (dict.description && dict.description.toLowerCase().includes(query))
    )
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(dict => dict.category_id === selectedCategory.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(dict => dict.status === selectedStatus.value)
  }

  return filtered
})

// 方法
const loadDictionaries = async () => {
  try {
    loading.value = true
    const response = await getDictionaries({
      page: currentPage.value,
      limit: pageSize.value,
      category_id: selectedCategory.value,
      search: searchQuery.value,
      status: selectedStatus.value
    })

    if (response.success) {
      dictionaries.value = response.data.list || []
      totalDictionaries.value = response.data.pagination?.total || 0
    }
  } catch (error) {
    console.error('加载字典列表失败:', error)
    ElMessage.error('加载字典列表失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await getDictionaryCategories()
    if (response.success) {
      categories.value = response.data || []
    }
  } catch (error) {
    console.error('加载字典分类失败:', error)
    ElMessage.error('加载字典分类失败')
  }
}

const refreshDictionaries = () => {
  currentPage.value = 1
  loadDictionaries()
}

const createDictionary = () => {
  currentDictionary.value = null
  editorVisible.value = true
}

const editDictionary = (dictionary) => {
  currentDictionary.value = dictionary
  editorVisible.value = true
}

const viewDictionary = (dictionary) => {
  currentDictionary.value = dictionary
  detailVisible.value = true
}

const copyDictionary = async (dictionary) => {
  try {
    const newDict = {
      ...dictionary,
      name: `${dictionary.name} - 副本`,
      code: `${dictionary.code}_copy_${Date.now()}`,
      status: 'draft'
    }
    delete newDict.id
    delete newDict.created_at
    delete newDict.updated_at

    currentDictionary.value = newDict
    editorVisible.value = true
  } catch (error) {
    console.error('复制字典失败:', error)
    ElMessage.error('复制字典失败')
  }
}

const toggleDictionaryStatus = async (dictionary) => {
  try {
    const newStatus = dictionary.status === 'active' ? 'inactive' : 'active'
    const action = newStatus === 'active' ? '启用' : '停用'

    await ElMessageBox.confirm(
      `确定要${action}字典 "${dictionary.name}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await updateDictionary(dictionary.id, { status: newStatus })
    ElMessage.success(`字典${action}成功`)
    loadDictionaries()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换字典状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const deleteDictionaryItem = async (dictionary) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字典 "${dictionary.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await deleteDictionary(dictionary.id)
    ElMessage.success('字典删除成功')
    loadDictionaries()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除字典失败:', error)
      ElMessage.error('删除失败')
    }
  }
}



const handleDictionarySaved = () => {
  editorVisible.value = false
  loadDictionaries()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadDictionaries()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadDictionaries()
}



const getVuexyStatusClass = (status) => {
  const statusMap = {
    active: 'status-success',
    inactive: 'status-secondary',
    draft: 'status-warning'
  }
  return statusMap[status] || 'status-secondary'
}

const getStatusType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'info',
    draft: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusName = (status) => {
  const statusMap = {
    active: '已启用',
    inactive: '已停用',
    draft: '草稿'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听搜索和筛选变化
watch([searchQuery, selectedCategory, selectedStatus], () => {
  currentPage.value = 1
  loadDictionaries()
}, { debounce: 300 })

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
  loadDictionaries()
})
</script>

<style lang="scss" scoped>
/* 主容器样式 */
.vuexy-dictionary-management {
  padding: 1.5rem;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem 1.5rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e8e6ea;
}

.filter-left {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex: 1;
}

.filter-right {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.page-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #5e5873;
  margin: 0;
  margin-right: 1.5rem;
}

/* 搜索框样式 */
.search-box {
  position: relative;
  width: 250px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #a8aaae;
  z-index: 2;
}

.search-input {
  width: 100%;
  height: 36px;
  padding: 0 12px 0 36px;
  border: 1px solid #e8e6ea;
  border-radius: 6px;
  background: #ffffff;
  color: #5e5873;
  font-size: 0.875rem;

  &::placeholder {
    color: #a8aaae;
  }

  &:focus {
    outline: none;
    border-color: #7367f0;
  }
}

/* 筛选选择器 */
.filter-select {
  height: 36px;
  padding: 0 12px;
  border: 1px solid #e8e6ea;
  border-radius: 6px;
  background: #ffffff;
  color: #5e5873;
  font-size: 0.875rem;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #7367f0;
  }
}

/* 选择框样式 */
.vuexy-select-wrapper {
  position: relative;
  min-width: 150px;
}

.vuexy-select {
  width: 100%;
  height: 40px;
  padding: 0 40px 0 12px;
  border: 1px solid #ebe9f1;
  border-radius: 8px;
  background: #ffffff;
  color: #5e5873;
  font-size: 0.875rem;
  cursor: pointer;
  appearance: none;
  transition: all 0.2s ease;
}

/* 暗色主题下的选择框 */
html.dark .vuexy-select {
  background: #283046;
  border-color: #404656;
  color: #d0d2d6;
}

.vuexy-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  pointer-events: none;
}

/* 按钮样式 */
.view-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #e8e6ea;
  border-radius: 6px;
  background: #ffffff;
  color: #6e6b7b;
  cursor: pointer;

  &:hover {
    border-color: #7367f0;
    color: #7367f0;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #e8e6ea;
  border-radius: 6px;
  background: #ffffff;
  color: #6e6b7b;
  cursor: pointer;

  &:hover {
    border-color: #7367f0;
    color: #7367f0;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 1rem;
  height: 36px;
  border: none;
  border-radius: 6px;
  background: #7367f0;
  color: #ffffff;
  font-size: 0.875rem;
  cursor: pointer;

  &:hover {
    background: #5e50ee;
  }
}

/* 表格容器 */
.table-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.table-wrapper {
  overflow-x: auto;
}

/* 现代表格样式 */
.modern-table {
  .el-table__header-wrapper {
    background: #f8f9fa;
  }

  .el-table__header th {
    background: #f8f9fa !important;
    color: #6e6b7b;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e8e6ea;
    padding: 1rem 1.25rem;
  }

  .el-table__body td {
    padding: 1.25rem 1.25rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
  }

  .el-table__row:hover {
    background: #f8f9fa !important;
  }
}

/* 字典信息样式 */
.dictionary-info {
  .dictionary-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .dictionary-name {
    font-size: 1rem;
    font-weight: 600;
    color: #5e5873;
    margin: 0;
  }

  .category-badge {
    padding: 0.25rem 0.75rem;
    background: rgba(115, 103, 240, 0.1);
    color: #7367f0;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .dictionary-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #a8aaae;
  }

  .code {
    font-family: 'Monaco', 'Menlo', monospace;
    background: #f8f9fa;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-size: 0.75rem;
  }

  .divider {
    color: #d8d6de;
  }

  .item-count, .version {
    font-weight: 500;
  }
}

/* 描述信息样式 */
.description-info {
  .description-text {
    color: #6e6b7b;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0 0 0.75rem 0;
  }

  .meta-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .update-time {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    font-size: 0.75rem;
    color: #a8aaae;

    svg {
      width: 14px;
      height: 14px;
    }
  }
}

/* 状态标签样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;

  .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
  }

  &.active {
    background: rgba(40, 199, 111, 0.1);
    color: #28c76f;
  }

  &.inactive {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
  }

  &.draft {
    background: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
  }
}

/* 表格操作按钮 */
.table-actions {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  flex-wrap: nowrap;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #e8e6ea;
  border-radius: 6px;
  background: #ffffff;
  color: #a8aaae;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  &.edit:hover {
    border-color: #ff9f43;
    color: #ff9f43;
    background: rgba(255, 159, 67, 0.1);
  }

  &.view:hover {
    border-color: #00cfe8;
    color: #00cfe8;
    background: rgba(0, 207, 232, 0.1);
  }

  &.copy:hover {
    border-color: #7367f0;
    color: #7367f0;
    background: rgba(115, 103, 240, 0.1);
  }

  &.enable:hover {
    border-color: #28c76f;
    color: #28c76f;
    background: rgba(40, 199, 111, 0.1);
  }

  &.disable:hover {
    border-color: #ff9f43;
    color: #ff9f43;
    background: rgba(255, 159, 67, 0.1);
  }

  &.delete:hover {
    border-color: #ea5455;
    color: #ea5455;
    background: rgba(234, 84, 85, 0.1);
  }
}

/* 表格操作按钮容器 */
.vuexy-table-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: nowrap;
}

/* 操作按钮样式 */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 0.75rem;
  border: 1px solid #ebe9f1;
  border-radius: 6px;
  background: #ffffff;
  color: #5e5873;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

/* 表格中的图标按钮（固定尺寸） */
.action-btn.icon-only {
  width: 32px;
  height: 32px;
  padding: 0;
  gap: 0;
}

/* 暗色主题下的操作按钮 */
html.dark .action-btn {
  background: #283046;
  border-color: #404656;
  color: #d0d2d6;
}

/* SVG 图标样式 */
.action-btn svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  fill: none;
}

.action-btn:hover {
  border-color: #7367f0;
  color: #7367f0;
  background: rgba(115, 103, 240, 0.08);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(115, 103, 240, 0.15);
}

.action-btn.primary {
  background: #7367f0;
  border-color: #7367f0;
  color: white;
}

.action-btn.primary:hover {
  background: #5e50ee;
  border-color: #5e50ee;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(115, 103, 240, 0.4);
}

/* 编辑按钮 */
.action-btn.edit-btn:hover {
  border-color: #00cfe8;
  color: #00cfe8;
  background: rgba(0, 207, 232, 0.08);
}

/* 查看按钮 */
.action-btn.info-btn:hover {
  border-color: #39da8a;
  color: #39da8a;
  background: rgba(57, 218, 138, 0.08);
}

/* 复制按钮 */
.action-btn.primary-btn:hover {
  border-color: #7367f0;
  color: #7367f0;
  background: rgba(115, 103, 240, 0.08);
}

/* 启用/停用按钮 */
.action-btn.success-btn:hover {
  border-color: #28c76f;
  color: #28c76f;
  background: rgba(40, 199, 111, 0.08);
}

.action-btn.warning-btn:hover {
  border-color: #ff9f43;
  color: #ff9f43;
  background: rgba(255, 159, 67, 0.08);
}

/* 删除按钮 */
.action-btn.delete-btn:hover {
  border-color: #ea5455;
  color: #ea5455;
  background: rgba(234, 84, 85, 0.08);
}

/* 视图切换按钮样式 */
.view-mode-toggle {
  display: flex;
  border: 1px solid #ebe9f1;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 0.75rem;
}

/* 暗色主题下的视图切换容器 */
html.dark .view-mode-toggle {
  border-color: #404656;
}

.view-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: #ffffff;
  color: #b9b9c3;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* 暗色主题下的视图按钮 */
html.dark .view-btn {
  background: #283046;
  color: #b4b7bd;
}

.view-btn:first-child {
  border-right: 1px solid #ebe9f1;
}

/* 暗色主题下的视图按钮分隔线 */
html.dark .view-btn:first-child {
  border-right-color: #404656;
}

.view-btn:hover {
  background: var(--primary-color-light);
  color: var(--primary-color);
}

.view-btn.active {
  background: var(--primary-color);
  color: white;
}

.view-btn.active:hover {
  background: var(--primary-color-dark);
}

/* 使用全局CSS变量 */
.vuexy-dictionary-management {
  --primary-color: var(--primary-color);
  --primary-color-light: var(--primary-rgba);
  --primary-color-dark: var(--primary-dark);
  --background-color: var(--background-color);
  --card-background: var(--card-background);
  --text-color-primary: var(--text-primary);
  --text-color-secondary: var(--text-secondary);
  --border-color: var(--border-color);
  --success-color: var(--success-color);
  --warning-color: var(--warning-color);
  --danger-color: var(--danger-color);
  --info-color: var(--info-color);
}

/* 表格容器样式 */
.vuexy-table {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #ebe9f1;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 暗色主题下的表格容器 */
html.dark .vuexy-table {
  background: #2f3349;
  border-color: #404656;
}

/* 分页样式 */
.vuexy-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem 1.5rem;
  background: var(--card-background);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.pagination-info {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

/* 卡片视图样式 */
.card-view {
  margin-bottom: 1.5rem;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.dictionary-card {
  background: #ffffff;
  border: 1px solid #ebe9f1;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
}

/* 暗色主题下的卡片样式 */
html.dark .dictionary-card {
  background: #2f3349;
  border-color: #404656;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.dictionary-card:hover {
  box-shadow: 0 4px 12px rgba(115, 103, 240, 0.15);
  transform: translateY(-2px);
  border-color: #7367f0;
}

/* 暗色主题下的卡片悬停效果 */
html.dark .dictionary-card:hover {
  box-shadow: 0 4px 12px rgba(115, 103, 240, 0.25);
  border-color: #7367f0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem;
  border-bottom: 1px solid #ebe9f1;
  background: #fafafa;
}

/* 暗色主题下的卡片头部 */
html.dark .card-header {
  border-bottom-color: #404656;
  background: #283046;
}

.dict-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #5e5873;
  line-height: 1.4;
}

/* 暗色主题下的卡片标题 */
html.dark .dict-info h3 {
  color: #d0d2d6;
}

.dict-code {
  font-size: 0.75rem;
  color: #6e6b7b;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f8f8f8;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 0.5rem;
  border: 1px solid #ebe9f1;
}

/* 暗色主题下的代码样式 */
html.dark .dict-code {
  background: #3b4253;
  border-color: #404656;
  color: #b4b7bd;
}

.dict-category {
  font-size: 0.875rem;
  color: #7367f0;
  font-weight: 500;
}

.card-content {
  padding: 1.25rem;
}

.dict-description {
  margin: 0 0 1rem 0;
  color: #6e6b7b;
  font-size: 0.875rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 暗色主题下的描述文字 */
html.dark .dict-description {
  color: #b4b7bd;
}

.dict-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6e6b7b;
}

/* 暗色主题下的元数据 */
html.dark .meta-item {
  color: #b4b7bd;
}

.meta-item svg {
  color: #7367f0;
}

.card-actions {
  display: flex;
  justify-content: center;
  padding: 1rem 1.25rem;
  background: #fafafa;
  border-top: 1px solid #ebe9f1;
}

/* 暗色主题下的卡片操作区域 */
html.dark .card-actions {
  background: #283046;
  border-top-color: #404656;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.card-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #ebe9f1;
  border-radius: 6px;
  background: #ffffff;
  color: #5e5873;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* 暗色主题下的卡片操作按钮 */
html.dark .card-action-btn {
  background: #2f3349;
  border-color: #404656;
  color: #d0d2d6;
}

.card-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(115, 103, 240, 0.15);
}

.card-action-btn.edit:hover {
  border-color: #00cfe8;
  color: #00cfe8;
  background: rgba(0, 207, 232, 0.08);
}

.card-action-btn.view:hover {
  border-color: #39da8a;
  color: #39da8a;
  background: rgba(57, 218, 138, 0.08);
}

.card-action-btn.copy:hover {
  border-color: #7367f0;
  color: #7367f0;
  background: rgba(115, 103, 240, 0.08);
}

.card-action-btn.delete:hover {
  border-color: #ea5455;
  color: #ea5455;
  background: rgba(234, 84, 85, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vuexy-dictionary-management {
    padding: 1rem;
  }

  .vuexy-filter-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filter-left {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-right {
    justify-content: center;
    flex-wrap: wrap;
  }

  .vuexy-search-box {
    width: 100%;
  }

  .vuexy-select-wrapper {
    min-width: auto;
    width: 100%;
  }

  .action-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }

  .vuexy-pagination {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .cards-grid {
    grid-template-columns: 1fr;
  }

  .view-mode-toggle {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }

  .filter-right {
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>
