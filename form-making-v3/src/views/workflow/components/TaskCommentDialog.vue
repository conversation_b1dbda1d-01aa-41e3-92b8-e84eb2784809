<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加备注"
    width="500px"
    :before-close="handleClose"
  >
    <div class="task-comment-form" v-if="task">
      <!-- 任务信息 -->
      <div class="task-info">
        <h4>{{ task.title }}</h4>
        <p class="task-meta">{{ task.workflow_name }} - {{ task.step_name }}</p>
      </div>

      <!-- 备注表单 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="备注内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="4"
            placeholder="请输入备注内容..."
          />
        </el-form-item>

        <el-form-item label="备注类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio value="note">普通备注</el-radio>
            <el-radio value="reminder">提醒事项</el-radio>
            <el-radio value="important">重要说明</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否通知">
          <el-switch
            v-model="form.notify"
            active-text="通知相关人员"
            inactive-text="仅记录"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          添加备注
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'commented'])

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref(null)

const form = reactive({
  content: '',
  type: 'note',
  notify: false
})

const rules = {
  content: [
    { required: true, message: '请输入备注内容', trigger: 'blur' },
    { min: 5, message: '备注内容至少5个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择备注类型', trigger: 'change' }
  ]
}

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
  }
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    content: '',
    type: 'note',
    notify: false
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('commented', {
      taskId: props.task.id,
      ...form
    })
    
    dialogVisible.value = false
  } catch (error) {
    console.error('添加备注失败:', error)
    ElMessage.error('添加备注失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.task-info {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: 0.5rem;

  h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
  }

  .task-meta {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
</style>
