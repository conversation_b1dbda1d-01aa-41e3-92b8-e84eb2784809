<template>
  <el-dialog
    v-model="dialogVisible"
    title="处理任务"
    width="600px"
    :before-close="handleClose"
  >
    <div class="task-handle-form" v-if="task">
      <!-- 任务信息 -->
      <div class="task-info">
        <h4>{{ task.title }}</h4>
        <p class="task-meta">{{ task.workflow_name }} - {{ task.step_name }}</p>
        <p class="task-initiator">发起人：{{ task.initiator }}</p>
      </div>

      <!-- 处理表单 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="处理结果" prop="result">
          <el-radio-group v-model="form.result">
            <el-radio value="approve">同意</el-radio>
            <el-radio value="reject">拒绝</el-radio>
            <el-radio value="return">退回</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="处理意见" prop="comment">
          <el-input
            v-model="form.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入处理意见..."
          />
        </el-form-item>

        <el-form-item v-if="form.result === 'approve'" label="下一步骤">
          <el-select v-model="form.nextStep" placeholder="选择下一步骤">
            <el-option label="财务审批" value="finance" />
            <el-option label="总经理审批" value="manager" />
            <el-option label="完成" value="complete" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.result === 'approve'" label="指派给">
          <el-select v-model="form.assignee" placeholder="选择处理人">
            <el-option label="张财务" value="zhang_finance" />
            <el-option label="李经理" value="li_manager" />
            <el-option label="王总" value="wang_ceo" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'handled'])

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref(null)

const form = reactive({
  result: 'approve',
  comment: '',
  nextStep: '',
  assignee: ''
})

const rules = {
  result: [
    { required: true, message: '请选择处理结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入处理意见', trigger: 'blur' }
  ]
}

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
  }
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    result: 'approve',
    comment: '',
    nextStep: '',
    assignee: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('handled', {
      taskId: props.task.id,
      ...form
    })
    
    dialogVisible.value = false
  } catch (error) {
    console.error('处理任务失败:', error)
    ElMessage.error('处理任务失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.task-info {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: 0.5rem;

  h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
  }

  .task-meta {
    margin: 0 0 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }

  .task-initiator {
    margin: 0;
    color: var(--text-light);
    font-size: 0.875rem;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
</style>
