<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量导入字典项"
    width="800px"
    :before-close="handleClose"
    class="vuexy-dialog batch-import-dialog"
    :close-on-click-modal="false"
  >
    <div class="vuexy-dialog-content">
      <!-- 导入方式选择 -->
      <div class="import-method">
        <h4>选择导入方式</h4>
        <el-radio-group v-model="importMethod" size="large">
          <el-radio value="text">文本导入</el-radio>
          <el-radio value="file">文件导入</el-radio>
        </el-radio-group>
      </div>

      <!-- 文本导入 -->
      <div v-if="importMethod === 'text'" class="text-import">
        <h4>文本格式导入</h4>
        <div class="format-tips">
          <p>支持以下格式（每行一个字典项）：</p>
          <ul>
            <li><code>标签,值</code> - 基本格式</li>
            <li><code>标签,值,描述</code> - 包含描述</li>
            <li><code>标签,值,描述,排序</code> - 包含排序</li>
          </ul>
          <p>示例：</p>
          <pre class="example-text">男,male,男性,1
女,female,女性,2
未知,unknown,性别未知,3</pre>
        </div>
        
        <el-input
          v-model="importText"
          type="textarea"
          :rows="10"
          placeholder="请输入字典项数据，每行一个项目"
          size="large"
        />
      </div>

      <!-- 文件导入 -->
      <div v-if="importMethod === 'file'" class="file-import">
        <h4>文件导入</h4>
        <div class="format-tips">
          <p>支持 CSV、Excel 文件格式</p>
          <p>文件格式要求：</p>
          <ul>
            <li>第一列：标签（必填）</li>
            <li>第二列：值（必填）</li>
            <li>第三列：描述（可选）</li>
            <li>第四列：排序（可选，数字）</li>
          </ul>
        </div>
        
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="true"
          :limit="1"
          accept=".csv,.xlsx,.xls"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          drag
        >
          <div class="upload-area">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" class="upload-icon">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
              <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
              <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
              <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
            </svg>
            <p>点击或拖拽文件到此区域上传</p>
            <p class="upload-tip">支持 CSV、Excel 格式</p>
          </div>
        </el-upload>
      </div>

      <!-- 导入选项 -->
      <div class="import-options">
        <h4>导入选项</h4>
        <el-form label-width="120px">
          <el-form-item label="重复处理">
            <el-radio-group v-model="duplicateAction" size="large">
              <el-radio value="skip">跳过重复项</el-radio>
              <el-radio value="update">更新重复项</el-radio>
              <el-radio value="error">报错停止</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="默认状态">
            <el-radio-group v-model="defaultStatus" size="large">
              <el-radio value="active">启用</el-radio>
              <el-radio value="inactive">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预览数据 -->
      <div v-if="previewData.length > 0" class="preview-section">
        <h4>数据预览 ({{ previewData.length }} 项)</h4>
        <div class="preview-table">
          <el-table
            :data="previewData.slice(0, 10)"
            style="width: 100%"
            max-height="300"
          >
            <el-table-column prop="label" label="标签" width="150" />
            <el-table-column prop="value" label="值" width="150" />
            <el-table-column prop="description" label="描述" show-overflow-tooltip />
            <el-table-column prop="sort_order" label="排序" width="80" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <span :class="row.status === 'active' ? 'text-success' : 'text-secondary'">
                  {{ row.status === 'active' ? '启用' : '停用' }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="previewData.length > 10" class="preview-more">
            还有 {{ previewData.length - 10 }} 项数据...
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="errors.length > 0" class="error-section">
        <h4>数据错误 ({{ errors.length }} 项)</h4>
        <div class="error-list">
          <div v-for="(error, index) in errors.slice(0, 5)" :key="index" class="error-item">
            <span class="error-line">第 {{ error.line }} 行:</span>
            <span class="error-message">{{ error.message }}</span>
          </div>
          <div v-if="errors.length > 5" class="error-more">
            还有 {{ errors.length - 5 }} 个错误...
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="vuexy-dialog-footer">
        <button class="action-btn" @click="handleClose">
          取消
        </button>
        <button class="action-btn" @click="parseData" :disabled="!canParse">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
          解析数据
        </button>
        <button 
          class="action-btn primary" 
          @click="importData" 
          :disabled="!canImport || importing"
        >
          <svg v-if="importing" width="16" height="16" viewBox="0 0 24 24" fill="none" class="animate-spin">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.3"/>
            <path d="M12 2a10 10 0 0 1 10 10" stroke="currentColor" stroke-width="4"/>
          </svg>
          {{ importing ? '导入中...' : '开始导入' }}
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { updateDictionary } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dictionary: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'imported'])

// 响应式数据
const dialogVisible = ref(false)
const importing = ref(false)
const importMethod = ref('text')
const importText = ref('')
const selectedFile = ref(null)
const duplicateAction = ref('skip')
const defaultStatus = ref('active')
const previewData = ref([])
const errors = ref([])
const uploadRef = ref(null)

// 计算属性
const canParse = computed(() => {
  if (importMethod.value === 'text') {
    return importText.value.trim().length > 0
  } else {
    return selectedFile.value !== null
  }
})

const canImport = computed(() => {
  return previewData.value.length > 0 && errors.value.length === 0
})

// 方法
const handleFileChange = (file) => {
  selectedFile.value = file
  previewData.value = []
  errors.value = []
}

const handleFileRemove = () => {
  selectedFile.value = null
  previewData.value = []
  errors.value = []
}

const parseData = async () => {
  try {
    previewData.value = []
    errors.value = []
    
    if (importMethod.value === 'text') {
      parseTextData()
    } else {
      await parseFileData()
    }
    
    if (errors.value.length === 0) {
      ElMessage.success(`成功解析 ${previewData.value.length} 条数据`)
    } else {
      ElMessage.warning(`解析完成，发现 ${errors.value.length} 个错误`)
    }
  } catch (error) {
    console.error('解析数据失败:', error)
    ElMessage.error('解析数据失败')
  }
}

const parseTextData = () => {
  const lines = importText.value.trim().split('\n')
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1
    const trimmedLine = line.trim()
    
    if (!trimmedLine) return
    
    const parts = trimmedLine.split(',').map(part => part.trim())
    
    if (parts.length < 2) {
      errors.value.push({
        line: lineNumber,
        message: '数据格式错误，至少需要标签和值两列'
      })
      return
    }
    
    const [label, value, description = '', sortOrder = '0'] = parts
    
    if (!label || !value) {
      errors.value.push({
        line: lineNumber,
        message: '标签和值不能为空'
      })
      return
    }
    
    const sort_order = parseInt(sortOrder) || 0
    
    previewData.value.push({
      label,
      value,
      description,
      sort_order,
      status: defaultStatus.value
    })
  })
}

const parseFileData = async () => {
  if (!selectedFile.value) return
  
  const file = selectedFile.value.raw
  const fileName = file.name.toLowerCase()
  
  if (fileName.endsWith('.csv')) {
    await parseCSVFile(file)
  } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    ElMessage.warning('Excel文件解析功能开发中，请使用CSV格式')
  } else {
    errors.value.push({
      line: 1,
      message: '不支持的文件格式'
    })
  }
}

const parseCSVFile = async (file) => {
  const text = await file.text()
  const lines = text.split('\n')
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1
    const trimmedLine = line.trim()
    
    if (!trimmedLine) return
    
    // 简单的CSV解析（不处理引号内的逗号）
    const parts = trimmedLine.split(',').map(part => part.trim().replace(/^"|"$/g, ''))
    
    if (parts.length < 2) {
      errors.value.push({
        line: lineNumber,
        message: '数据格式错误，至少需要标签和值两列'
      })
      return
    }
    
    const [label, value, description = '', sortOrder = '0'] = parts
    
    if (!label || !value) {
      errors.value.push({
        line: lineNumber,
        message: '标签和值不能为空'
      })
      return
    }
    
    const sort_order = parseInt(sortOrder) || 0
    
    previewData.value.push({
      label,
      value,
      description,
      sort_order,
      status: defaultStatus.value
    })
  })
}

const importData = async () => {
  try {
    importing.value = true
    
    // 获取当前字典的项目
    const currentItems = props.dictionary.items || []
    const newItems = [...currentItems]
    
    // 处理重复项
    for (const newItem of previewData.value) {
      const existingIndex = newItems.findIndex(item => item.value === newItem.value)
      
      if (existingIndex !== -1) {
        // 发现重复项
        if (duplicateAction.value === 'skip') {
          continue
        } else if (duplicateAction.value === 'update') {
          newItems[existingIndex] = { ...newItems[existingIndex], ...newItem }
        } else if (duplicateAction.value === 'error') {
          throw new Error(`发现重复的字典值: ${newItem.value}`)
        }
      } else {
        // 新项目
        newItems.push(newItem)
      }
    }
    
    // 更新字典
    await updateDictionary(props.dictionary.id, {
      items: newItems
    })
    
    ElMessage.success(`成功导入 ${previewData.value.length} 条数据`)
    emit('imported')
    handleClose()
  } catch (error) {
    console.error('导入数据失败:', error)
    ElMessage.error(error.message || '导入数据失败')
  } finally {
    importing.value = false
  }
}

const resetData = () => {
  importText.value = ''
  selectedFile.value = null
  previewData.value = []
  errors.value = []
  duplicateAction.value = 'skip'
  defaultStatus.value = 'active'
  importMethod.value = 'text'
  
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetData()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style lang="scss" scoped>
.vuexy-dialog-content {
  padding: 0;
}

.import-method,
.text-import,
.file-import,
.import-options,
.preview-section,
.error-section {
  margin-bottom: 1.5rem;
  
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color-primary);
  }
}

.format-tips {
  background: var(--background-color-light);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  
  p {
    margin: 0 0 0.5rem 0;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
  }
  
  ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
    
    li {
      margin-bottom: 0.25rem;
      color: var(--text-color-secondary);
      font-size: 0.875rem;
      
      code {
        background: var(--card-background);
        padding: 0.125rem 0.25rem;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.75rem;
      }
    }
  }
  
  .example-text {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.75rem;
    margin: 0.5rem 0 0 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
    color: var(--text-color-primary);
    overflow-x: auto;
  }
}

.upload-area {
  text-align: center;
  padding: 2rem;
  
  .upload-icon {
    color: var(--text-color-secondary);
    margin-bottom: 1rem;
  }
  
  p {
    margin: 0.5rem 0;
    color: var(--text-color-primary);
  }
  
  .upload-tip {
    font-size: 0.75rem;
    color: var(--text-color-secondary);
  }
}

.preview-table {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.preview-more,
.error-more {
  padding: 0.75rem;
  text-align: center;
  background: var(--background-color-light);
  color: var(--text-color-secondary);
  font-size: 0.875rem;
  border-top: 1px solid var(--border-color);
}

.error-list {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 1rem;
  
  .error-item {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    
    .error-line {
      font-weight: 600;
      color: #dc2626;
      margin-right: 0.5rem;
    }
    
    .error-message {
      color: #7f1d1d;
    }
  }
}

.text-success {
  color: #16a34a;
}

.text-secondary {
  color: #6b7280;
}

.vuexy-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 0 0 0;
  border-top: 1px solid var(--border-color);
  margin-top: 1.5rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 36px;
  padding: 0 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: #ffffff;
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn:disabled:hover {
  border-color: var(--border-color);
  color: var(--text-color-primary);
  background: #ffffff;
}

.action-btn.primary:disabled:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 暗色主题适配 */
html.dark .action-btn {
  background: #283046;
  border-color: #404656;
  color: #d0d2d6;
}

html.dark .action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

html.dark .action-btn:disabled:hover {
  border-color: #404656;
  color: #d0d2d6;
  background: #283046;
}

html.dark .format-tips {
  background: #3b4253;
  border-color: #404656;
}

html.dark .format-tips .example-text {
  background: #283046;
  border-color: #404656;
}

html.dark .error-list {
  background: #3b2f2f;
  border-color: #5c3333;
}

html.dark .vuexy-dialog-footer {
  border-top-color: #404656;
}
</style>
