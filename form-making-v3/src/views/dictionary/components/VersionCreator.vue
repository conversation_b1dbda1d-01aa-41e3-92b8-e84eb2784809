<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建版本"
    width="600px"
    :before-close="handleClose"
    class="vuexy-dialog version-creator-dialog"
    :close-on-click-modal="false"
  >
    <div class="vuexy-dialog-content">
      <!-- 字典信息 -->
      <div v-if="dictionary" class="dictionary-summary">
        <div class="summary-header">
          <div class="dict-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
              <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
              <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="dict-info">
            <h4>{{ dictionary.name }}</h4>
            <p>当前版本: v{{ dictionary.version }}</p>
          </div>
          <div class="dict-stats">
            <div class="stat-item">
              <div class="stat-number">{{ itemCount }}</div>
              <div class="stat-label">数据项</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 版本信息表单 -->
      <el-form
        ref="formRef"
        :model="versionForm"
        :rules="formRules"
        label-width="100px"
        class="vuexy-form"
      >
        <el-form-item label="版本号" prop="version" class="required-field">
          <el-input
            v-model="versionForm.version"
            placeholder="请输入版本号（如：1.1.0）"
            size="large"
          />
          <div class="form-tip">
            建议使用语义化版本号，如：主版本.次版本.修订版本
          </div>
        </el-form-item>

        <el-form-item label="变更日志" prop="change_log" class="required-field">
          <el-input
            v-model="versionForm.change_log"
            type="textarea"
            :rows="4"
            placeholder="请描述本次版本的主要变更内容"
            size="large"
          />
          <div class="form-tip">
            详细描述本次版本的变更内容，便于后续版本管理和回滚
          </div>
        </el-form-item>

        <el-form-item label="版本类型" prop="version_type">
          <el-radio-group v-model="versionForm.version_type" size="large">
            <el-radio value="major">主版本</el-radio>
            <el-radio value="minor">次版本</el-radio>
            <el-radio value="patch">修订版本</el-radio>
          </el-radio-group>
          <div class="form-tip">
            主版本：不兼容的API修改；次版本：向下兼容的功能性新增；修订版本：向下兼容的问题修正
          </div>
        </el-form-item>

        <el-form-item label="自动更新">
          <el-checkbox v-model="versionForm.auto_update_dictionary">
            同时更新字典版本号
          </el-checkbox>
          <div class="form-tip">
            勾选后将同时更新字典的当前版本号为新创建的版本
          </div>
        </el-form-item>
      </el-form>

      <!-- 数据快照预览 -->
      <div class="snapshot-preview">
        <h4>数据快照预览</h4>
        <div class="preview-stats">
          <div class="stat-item">
            <span class="stat-label">数据项总数:</span>
            <span class="stat-value">{{ itemCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">启用项数:</span>
            <span class="stat-value">{{ activeItemCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">停用项数:</span>
            <span class="stat-value">{{ inactiveItemCount }}</span>
          </div>
        </div>
        
        <div class="preview-note">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M12 16v-4" stroke="currentColor" stroke-width="2"/>
            <path d="M12 8h.01" stroke="currentColor" stroke-width="2"/>
          </svg>
          <span>版本将保存当前字典的所有数据项快照，包括标签、值、描述、排序和状态信息</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="vuexy-dialog-footer">
        <button class="action-btn" @click="handleClose">
          取消
        </button>
        <button class="action-btn primary" @click="createVersion" :disabled="creating">
          <svg v-if="creating" width="16" height="16" viewBox="0 0 24 24" fill="none" class="animate-spin">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.3"/>
            <path d="M12 2a10 10 0 0 1 10 10" stroke="currentColor" stroke-width="4"/>
          </svg>
          {{ creating ? '创建中...' : '创建版本' }}
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createDictionaryVersion, updateDictionary } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dictionary: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'created'])

// 响应式数据
const dialogVisible = ref(false)
const creating = ref(false)
const formRef = ref(null)

// 表单数据
const versionForm = reactive({
  version: '',
  change_log: '',
  version_type: 'minor',
  auto_update_dictionary: true
})

// 表单验证规则
const formRules = {
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，请使用 x.y.z 格式', trigger: 'blur' }
  ],
  change_log: [
    { required: true, message: '请输入变更日志', trigger: 'blur' },
    { min: 10, max: 1000, message: '变更日志长度在 10 到 1000 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const itemCount = computed(() => {
  if (!props.dictionary?.items) return 0
  return props.dictionary.items.length
})

const activeItemCount = computed(() => {
  if (!props.dictionary?.items) return 0
  return props.dictionary.items.filter(item => item.status === 'active').length
})

const inactiveItemCount = computed(() => {
  if (!props.dictionary?.items) return 0
  return props.dictionary.items.filter(item => item.status === 'inactive').length
})

// 方法
const generateNextVersion = () => {
  if (!props.dictionary?.version) {
    return '1.0.0'
  }
  
  const currentVersion = props.dictionary.version
  const parts = currentVersion.split('.').map(Number)
  
  if (parts.length !== 3) {
    return '1.0.0'
  }
  
  let [major, minor, patch] = parts
  
  switch (versionForm.version_type) {
    case 'major':
      major += 1
      minor = 0
      patch = 0
      break
    case 'minor':
      minor += 1
      patch = 0
      break
    case 'patch':
      patch += 1
      break
  }
  
  return `${major}.${minor}.${patch}`
}

const resetForm = () => {
  Object.assign(versionForm, {
    version: '',
    change_log: '',
    version_type: 'minor',
    auto_update_dictionary: true
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const initFormData = () => {
  resetForm()
  versionForm.version = generateNextVersion()
}

const createVersion = async () => {
  try {
    await formRef.value.validate()
    
    creating.value = true
    
    // 准备版本数据
    const versionData = {
      version: versionForm.version,
      change_log: versionForm.change_log,
      items_data: props.dictionary.items || []
    }
    
    // 创建版本
    await createDictionaryVersion(props.dictionary.id, versionData)
    
    // 如果需要自动更新字典版本号
    if (versionForm.auto_update_dictionary) {
      await updateDictionary(props.dictionary.id, {
        version: versionForm.version
      })
    }
    
    ElMessage.success('版本创建成功')
    emit('created')
    handleClose()
  } catch (error) {
    if (error !== 'validation failed') {
      console.error('创建版本失败:', error)
      ElMessage.error('创建版本失败')
    }
  } finally {
    creating.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听版本类型变化，自动生成版本号
watch(() => versionForm.version_type, () => {
  versionForm.version = generateNextVersion()
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initFormData()
  } else {
    resetForm()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style lang="scss" scoped>
.vuexy-dialog-content {
  padding: 0;
}

.dictionary-summary {
  background: var(--background-color-light);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  
  .summary-header {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .dict-icon {
    width: 40px;
    height: 40px;
    background: rgba(115, 103, 240, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
  }
  
  .dict-info {
    flex: 1;
    
    h4 {
      margin: 0 0 0.25rem 0;
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-color-primary);
    }
    
    p {
      margin: 0;
      font-size: 0.875rem;
      color: var(--text-color-secondary);
    }
  }
  
  .dict-stats {
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
        line-height: 1;
      }
      
      .stat-label {
        font-size: 0.75rem;
        color: var(--text-color-secondary);
        margin-top: 0.25rem;
      }
    }
  }
}

.vuexy-form {
  .required-field :deep(.el-form-item__label::before) {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
}

.form-tip {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
  margin-top: 0.25rem;
  line-height: 1.4;
}

.snapshot-preview {
  background: var(--background-color-light);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
  
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color-primary);
  }
  
  .preview-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
    
    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .stat-label {
        font-size: 0.875rem;
        color: var(--text-color-secondary);
      }
      
      .stat-value {
        font-weight: 600;
        color: var(--primary-color);
      }
    }
  }
  
  .preview-note {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(115, 103, 240, 0.05);
    border: 1px solid rgba(115, 103, 240, 0.2);
    border-radius: 6px;
    
    svg {
      color: var(--primary-color);
      flex-shrink: 0;
      margin-top: 0.125rem;
    }
    
    span {
      font-size: 0.875rem;
      color: var(--text-color-secondary);
      line-height: 1.4;
    }
  }
}

.vuexy-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 0 0 0;
  border-top: 1px solid var(--border-color);
  margin-top: 1.5rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 36px;
  padding: 0 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: #ffffff;
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn:disabled:hover {
  border-color: var(--border-color);
  color: var(--text-color-primary);
  background: #ffffff;
}

.action-btn.primary:disabled:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 暗色主题适配 */
html.dark .action-btn {
  background: #283046;
  border-color: #404656;
  color: #d0d2d6;
}

html.dark .action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

html.dark .action-btn:disabled:hover {
  border-color: #404656;
  color: #d0d2d6;
  background: #283046;
}

html.dark .dictionary-summary {
  background: #3b4253;
  border-color: #404656;
}

html.dark .snapshot-preview {
  background: #3b4253;
  border-color: #404656;
}

html.dark .form-tip {
  color: #b4b7bd;
}

html.dark .vuexy-dialog-footer {
  border-top-color: #404656;
}
</style>
