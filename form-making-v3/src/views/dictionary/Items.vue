<template>
  <div class="vuexy-dictionary-items">
    <!-- 字典选择区域 -->
    <div class="vuexy-filter-section">
      <div class="filter-left">
        <div class="vuexy-select-wrapper">
          <select v-model="selectedCategoryId" class="vuexy-select" @change="onCategoryChange">
            <option value="">选择字典分类</option>
            <option
              v-for="category in categories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedDictionaryId" class="vuexy-select" @change="onDictionaryChange" :disabled="!selectedCategoryId">
            <option value="">选择字典</option>
            <option
              v-for="dictionary in filteredDictionaries"
              :key="dictionary.id"
              :value="dictionary.id"
            >
              {{ dictionary.name }} ({{ dictionary.code }})
            </option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="vuexy-search-box" v-if="selectedDictionaryId">
          <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索字典项标签或值"
            class="search-input"
          />
        </div>
      </div>
      <div class="filter-right" v-if="selectedDictionaryId">
        <button @click="refreshItems" class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
          刷新
        </button>
        <button @click="createItem" class="action-btn primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
          </svg>
          添加字典项
        </button>
        <button @click="batchImport" class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
          </svg>
          批量导入
        </button>
      </div>
    </div>

    <!-- 字典信息卡片 -->
    <div v-if="currentDictionary" class="dictionary-info-card">
      <div class="card-header">
        <div class="dict-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="dict-details">
          <h3>{{ currentDictionary.name }}</h3>
          <p class="dict-code">{{ currentDictionary.code }}</p>
          <p class="dict-description">{{ currentDictionary.description || '暂无描述' }}</p>
        </div>
        <div class="dict-stats">
          <div class="stat-item">
            <div class="stat-number">{{ items.length }}</div>
            <div class="stat-label">字典项数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ currentDictionary.version }}</div>
            <div class="stat-label">当前版本</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 字典项表格 -->
    <div v-if="selectedDictionaryId" class="vuexy-table">
      <el-table
        :data="filteredItems"
        v-loading="loading"
        :stripe="false"
        :border="false"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="字典项信息" min-width="250">
          <template #default="{ row }">
            <div class="item-info">
              <div class="item-details">
                <div class="item-label">{{ row.label }}</div>
                <div class="item-value">值: {{ row.value }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="描述" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.description || '暂无描述' }}
          </template>
        </el-table-column>

        <el-table-column label="排序" width="80" align="center">
          <template #default="{ row }">
            <span class="sort-order">{{ row.sort_order }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <div class="vuexy-status-tag" :class="getVuexyStatusClass(row.status)">
              <div class="status-dot"></div>
              {{ getStatusName(row.status) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="vuexy-table-actions">
              <button class="action-btn icon-only edit-btn" @click="editItem(row)" title="编辑">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="action-btn icon-only primary-btn" @click="addChildItem(row)" title="添加子项">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                  <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button
                class="action-btn icon-only"
                :class="row.status === 'active' ? 'warning-btn' : 'success-btn'"
                @click="toggleItemStatus(row)"
                :title="row.status === 'active' ? '停用' : '启用'"
              >
                <svg v-if="row.status === 'active'" width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                  <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="action-btn icon-only delete-btn" @click="deleteItem(row)" title="删除">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                  <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="!selectedDictionaryId" class="empty-state">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="empty-icon">
        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
      </svg>
      <h3>选择字典</h3>
      <p>请先选择分类和字典，然后开始维护字典项数据</p>
    </div>

    <!-- 字典项编辑对话框 -->
    <ItemEditor
      v-model:visible="editorVisible"
      :item="currentItem"
      :dictionary="currentDictionary"
      :items="items"
      @saved="handleItemSaved"
    />

    <!-- 批量导入对话框 -->
    <BatchImport
      v-model:visible="importVisible"
      :dictionary="currentDictionary"
      @imported="handleItemsImported"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getDictionaryCategories,
  getDictionaries,
  getDictionary,
  updateDictionary
} from '@/util/api'
import ItemEditor from './components/ItemEditor.vue'
import BatchImport from './components/BatchImport.vue'

// 响应式数据
const loading = ref(false)
const categories = ref([])
const dictionaries = ref([])
const items = ref([])
const searchQuery = ref('')
const selectedCategoryId = ref('')
const selectedDictionaryId = ref('')
const currentDictionary = ref(null)

// 对话框状态
const editorVisible = ref(false)
const importVisible = ref(false)
const currentItem = ref(null)

// 计算属性
const filteredDictionaries = computed(() => {
  if (!selectedCategoryId.value) return []
  return dictionaries.value.filter(dict => dict.category_id == selectedCategoryId.value)
})

const filteredItems = computed(() => {
  if (!searchQuery.value) return items.value

  const query = searchQuery.value.toLowerCase()
  return items.value.filter(item =>
    item.label.toLowerCase().includes(query) ||
    item.value.toLowerCase().includes(query) ||
    (item.description && item.description.toLowerCase().includes(query))
  )
})

// 方法
const loadCategories = async () => {
  try {
    const response = await getDictionaryCategories()
    if (response.success) {
      categories.value = response.data || []
    }
  } catch (error) {
    console.error('加载字典分类失败:', error)
    ElMessage.error('加载字典分类失败')
  }
}

const loadDictionaries = async () => {
  try {
    const response = await getDictionaries({ limit: 1000 })
    if (response.success) {
      dictionaries.value = response.data.list || []
    }
  } catch (error) {
    console.error('加载字典列表失败:', error)
    ElMessage.error('加载字典列表失败')
  }
}

const loadDictionaryItems = async () => {
  if (!selectedDictionaryId.value) return

  try {
    loading.value = true
    const response = await getDictionary(selectedDictionaryId.value)
    if (response.success) {
      currentDictionary.value = response.data
      items.value = buildItemTree(response.data.items || [])
    }
  } catch (error) {
    console.error('加载字典项失败:', error)
    ElMessage.error('加载字典项失败')
  } finally {
    loading.value = false
  }
}

// 构建字典项树形结构
const buildItemTree = (items) => {
  const itemMap = new Map()
  const rootItems = []

  // 先创建所有项的映射
  items.forEach(item => {
    item.children = []
    itemMap.set(item.id, item)
  })

  // 构建树形结构
  items.forEach(item => {
    if (item.parent_id) {
      const parent = itemMap.get(item.parent_id)
      if (parent) {
        parent.children.push(item)
      }
    } else {
      rootItems.push(item)
    }
  })

  return rootItems
}

const onCategoryChange = () => {
  selectedDictionaryId.value = ''
  currentDictionary.value = null
  items.value = []
}

const onDictionaryChange = () => {
  if (selectedDictionaryId.value) {
    loadDictionaryItems()
  } else {
    currentDictionary.value = null
    items.value = []
  }
}

const refreshItems = () => {
  loadDictionaryItems()
}

const createItem = () => {
  currentItem.value = null
  editorVisible.value = true
}

const editItem = (item) => {
  currentItem.value = item
  editorVisible.value = true
}

const addChildItem = (parentItem) => {
  currentItem.value = {
    parent_id: parentItem.id,
    parent_label: parentItem.label
  }
  editorVisible.value = true
}

const toggleItemStatus = async (item) => {
  try {
    const newStatus = item.status === 'active' ? 'inactive' : 'active'
    const action = newStatus === 'active' ? '启用' : '停用'

    await ElMessageBox.confirm(
      `确定要${action}字典项 "${item.label}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 更新字典项状态
    const updatedItems = updateItemInTree(items.value, item.id, { status: newStatus })
    await saveDictionaryItems(updatedItems)

    ElMessage.success(`字典项${action}成功`)
    loadDictionaryItems()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换字典项状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const deleteItem = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字典项 "${item.label}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    // 从树中删除项目
    const updatedItems = removeItemFromTree(items.value, item.id)
    await saveDictionaryItems(updatedItems)

    ElMessage.success('字典项删除成功')
    loadDictionaryItems()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除字典项失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 更新树中的项目
const updateItemInTree = (items, itemId, updates) => {
  return items.map(item => {
    if (item.id === itemId) {
      return { ...item, ...updates }
    }
    if (item.children && item.children.length > 0) {
      return {
        ...item,
        children: updateItemInTree(item.children, itemId, updates)
      }
    }
    return item
  })
}

// 从树中删除项目
const removeItemFromTree = (items, itemId) => {
  return items.filter(item => {
    if (item.id === itemId) {
      return false
    }
    if (item.children && item.children.length > 0) {
      item.children = removeItemFromTree(item.children, itemId)
    }
    return true
  })
}

// 保存字典项到后端
const saveDictionaryItems = async (items) => {
  const flatItems = flattenItemTree(items)
  await updateDictionary(selectedDictionaryId.value, {
    items: flatItems
  })
}

// 扁平化字典项树
const flattenItemTree = (items) => {
  const result = []
  const flatten = (items, parentId = null) => {
    items.forEach(item => {
      result.push({
        label: item.label,
        value: item.value,
        description: item.description,
        sort_order: item.sort_order,
        parent_id: parentId,
        extra_data: item.extra_data,
        status: item.status
      })
      if (item.children && item.children.length > 0) {
        flatten(item.children, item.id)
      }
    })
  }
  flatten(items)
  return result
}

const batchImport = () => {
  importVisible.value = true
}

const handleItemSaved = () => {
  editorVisible.value = false
  loadDictionaryItems()
}

const handleItemsImported = () => {
  importVisible.value = false
  loadDictionaryItems()
}

const getVuexyStatusClass = (status) => {
  const statusMap = {
    active: 'status-success',
    inactive: 'status-secondary'
  }
  return statusMap[status] || 'status-secondary'
}

const getStatusName = (status) => {
  const statusMap = {
    active: '已启用',
    inactive: '已停用'
  }
  return statusMap[status] || '未知'
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
  loadDictionaries()
})
</script>

<style lang="scss" scoped>
/* 字典维护页面样式 */
.vuexy-dictionary-items {
  padding: 1.5rem;
}

/* 筛选区域样式 */
.vuexy-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 搜索框样式 */
.vuexy-search-box {
  position: relative;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  z-index: 2;
}

.search-input {
  width: 100%;
  height: 36px;
  padding: 0 16px 0 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

/* 选择器样式 */
.vuexy-select-wrapper {
  position: relative;
  min-width: 180px;
}

.vuexy-select {
  width: 100%;
  height: 36px;
  padding: 0 40px 0 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  cursor: pointer;
  appearance: none;
  transition: all 0.2s ease;
}

.vuexy-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

.vuexy-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  pointer-events: none;
}

/* 按钮样式 */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 36px;
  padding: 0 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.action-btn.icon-only {
  min-width: 36px;
  padding: 0;
}

.action-btn.edit-btn:hover {
  border-color: #ff9f43;
  color: #ff9f43;
  background: rgba(255, 159, 67, 0.08);
}

.action-btn.primary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.warning-btn:hover {
  border-color: #ff9f43;
  color: #ff9f43;
  background: rgba(255, 159, 67, 0.08);
}

.action-btn.success-btn:hover {
  border-color: #28c76f;
  color: #28c76f;
  background: rgba(40, 199, 111, 0.08);
}

.action-btn.delete-btn:hover {
  border-color: #ea5455;
  color: #ea5455;
  background: rgba(234, 84, 85, 0.08);
}

/* 表格样式 */
.vuexy-table {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.vuexy-table-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 状态标签样式 */
.vuexy-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.status-success {
  background: rgba(40, 199, 111, 0.1);
  color: #28c76f;
}

.status-secondary {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

/* 字典维护特有样式 */

/* 字典维护特有样式 */
.dictionary-info-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.dict-icon {
  width: 48px;
  height: 48px;
  background: rgba(115, 103, 240, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  flex-shrink: 0;
}

.dict-details {
  flex: 1;

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color-primary);
  }

  .dict-code {
    font-size: 0.875rem;
    color: var(--text-color-secondary);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    margin: 0 0 0.5rem 0;
  }

  .dict-description {
    font-size: 0.875rem;
    color: var(--text-color-secondary);
    margin: 0;
    line-height: 1.5;
  }
}

.dict-stats {
  display: flex;
  gap: 2rem;

  .stat-item {
    text-align: center;

    .stat-number {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--primary-color);
      line-height: 1;
    }

    .stat-label {
      font-size: 0.75rem;
      color: var(--text-color-secondary);
      margin-top: 0.25rem;
    }
  }
}

.item-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.item-details {
  flex: 1;
}

.item-label {
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 0.25rem;
}

.item-value {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: var(--background-color-light);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  display: inline-block;
}

.sort-order {
  font-weight: 600;
  color: var(--text-color-primary);
}

.empty-state {
  text-align: center;
  padding: 4rem 1rem;

  .empty-icon {
    color: var(--text-color-secondary);
    margin-bottom: 1rem;
  }

  h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color-primary);
    font-weight: 600;
  }

  p {
    margin: 0;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
  }
}

/* 暗色主题适配 */
html.dark .item-value {
  background: #3b4253;
  border-color: #404656;
  color: #b4b7bd;
}

html.dark .item-label {
  color: #d0d2d6;
}

html.dark .sort-order {
  color: #d0d2d6;
}

html.dark .dictionary-info-card {
  background: #283046;
  border-color: #404656;
}

html.dark .dict-details h3 {
  color: #d0d2d6;
}

html.dark .dict-code,
html.dark .dict-description {
  color: #b4b7bd;
}
</style>
