<template>
  <el-dialog
    v-model="dialogVisible"
    :title="category?.id ? '编辑分类' : '创建分类'"
    width="600px"
    :before-close="handleClose"
    class="vuexy-dialog category-editor-dialog"
    :close-on-click-modal="false"
  >
    <div class="vuexy-dialog-content">
      <el-form
        ref="formRef"
        :model="categoryForm"
        :rules="formRules"
        label-width="100px"
        class="vuexy-form"
      >
        <el-form-item label="分类名称" prop="name" class="required-field">
          <el-input
            v-model="categoryForm.name"
            placeholder="请输入分类名称"
            size="large"
          />
        </el-form-item>

        <el-form-item label="分类编码" prop="code" class="required-field">
          <el-input
            v-model="categoryForm.code"
            placeholder="请输入分类编码（英文字母、数字、下划线）"
            size="large"
            :disabled="!!category?.id"
          />
          <div class="form-tip">
            分类编码用于系统内部标识，创建后不可修改
          </div>
        </el-form-item>

        <el-form-item label="父分类" prop="parent_id">
          <el-select
            v-model="categoryForm.parent_id"
            placeholder="请选择父分类（可选）"
            size="large"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="cat in availableParentCategories"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            >
              <span>{{ cat.name }}</span>
              <span style="color: #8492a6; font-size: 13px; margin-left: 8px">{{ cat.code }}</span>
            </el-option>
          </el-select>
          <div v-if="category?.parent_name" class="form-tip">
            当前父分类：{{ category.parent_name }}
          </div>
        </el-form-item>

        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
            size="large"
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="categoryForm.sort_order"
            :min="0"
            :max="9999"
            size="large"
            style="width: 200px"
          />
          <div class="form-tip">
            数值越小排序越靠前
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="categoryForm.status" size="large">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="vuexy-dialog-footer">
        <button class="action-btn" @click="handleClose">
          取消
        </button>
        <button class="action-btn primary" @click="saveCategory" :disabled="saving">
          <svg v-if="saving" width="16" height="16" viewBox="0 0 24 24" fill="none" class="animate-spin">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.3"/>
            <path d="M12 2a10 10 0 0 1 10 10" stroke="currentColor" stroke-width="4"/>
          </svg>
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { createDictionaryCategory, updateDictionaryCategory } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  category: {
    type: Object,
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'saved'])

// 响应式数据
const dialogVisible = ref(false)
const saving = ref(false)
const formRef = ref(null)

// 表单数据
const categoryForm = reactive({
  name: '',
  code: '',
  parent_id: null,
  description: '',
  sort_order: 0,
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { min: 2, max: 50, message: '分类编码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '分类编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', message: '排序必须是数字', trigger: 'blur' }
  ]
}

// 计算可用的父分类（排除自己和子分类）
const availableParentCategories = computed(() => {
  if (!props.categories) return []
  
  const flatCategories = flattenCategories(props.categories)
  
  if (!props.category?.id) {
    return flatCategories
  }
  
  // 排除自己和自己的子分类
  const excludeIds = new Set([props.category.id])
  collectChildrenIds(props.category, excludeIds)
  
  return flatCategories.filter(cat => !excludeIds.has(cat.id))
})

// 扁平化分类树
const flattenCategories = (categories, level = 0) => {
  const result = []
  categories.forEach(category => {
    result.push({
      ...category,
      name: '　'.repeat(level) + category.name
    })
    if (category.children && category.children.length > 0) {
      result.push(...flattenCategories(category.children, level + 1))
    }
  })
  return result
}

// 收集子分类ID
const collectChildrenIds = (category, excludeIds) => {
  if (category.children) {
    category.children.forEach(child => {
      excludeIds.add(child.id)
      collectChildrenIds(child, excludeIds)
    })
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(categoryForm, {
    name: '',
    code: '',
    parent_id: null,
    description: '',
    sort_order: 0,
    status: 'active'
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 初始化表单数据
const initFormData = () => {
  if (props.category) {
    if (props.category.id) {
      // 编辑模式
      Object.assign(categoryForm, {
        name: props.category.name || '',
        code: props.category.code || '',
        parent_id: props.category.parent_id || null,
        description: props.category.description || '',
        sort_order: props.category.sort_order || 0,
        status: props.category.status || 'active'
      })
    } else {
      // 新建子分类模式
      Object.assign(categoryForm, {
        name: '',
        code: '',
        parent_id: props.category.parent_id || null,
        description: '',
        sort_order: 0,
        status: 'active'
      })
    }
  } else {
    resetForm()
  }
}

// 保存分类
const saveCategory = async () => {
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    const data = { ...categoryForm }
    
    if (props.category?.id) {
      // 编辑模式
      await updateDictionaryCategory(props.category.id, data)
      ElMessage.success('分类更新成功')
    } else {
      // 新建模式
      await createDictionaryCategory(data)
      ElMessage.success('分类创建成功')
    }
    
    emit('saved')
    handleClose()
  } catch (error) {
    if (error !== 'validation failed') {
      console.error('保存分类失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initFormData()
  } else {
    resetForm()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style lang="scss" scoped>
.vuexy-dialog-content {
  padding: 0;
}

.vuexy-form {
  .required-field :deep(.el-form-item__label::before) {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
}

.form-tip {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
  margin-top: 0.25rem;
  line-height: 1.4;
}

.vuexy-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 0 0 0;
  border-top: 1px solid var(--border-color);
  margin-top: 1.5rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 36px;
  padding: 0 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: #ffffff;
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn:disabled:hover {
  border-color: var(--border-color);
  color: var(--text-color-primary);
  background: #ffffff;
}

.action-btn.primary:disabled:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 暗色主题适配 */
html.dark .action-btn {
  background: #283046;
  border-color: #404656;
  color: #d0d2d6;
}

html.dark .action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

html.dark .action-btn:disabled:hover {
  border-color: #404656;
  color: #d0d2d6;
  background: #283046;
}

html.dark .form-tip {
  color: #b4b7bd;
}

html.dark .vuexy-dialog-footer {
  border-top-color: #404656;
}
</style>
