<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="task-detail" v-if="task">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4>基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>任务标题：</label>
            <span>{{ task.title }}</span>
          </div>
          <div class="info-item">
            <label>工作流：</label>
            <span>{{ task.workflow_name }}</span>
          </div>
          <div class="info-item">
            <label>当前步骤：</label>
            <span>{{ task.step_name }}</span>
          </div>
          <div class="info-item">
            <label>任务状态：</label>
            <div class="vuexy-status-tag" :class="getStatusClass(task.status)">
              <div class="status-dot"></div>
              {{ getStatusName(task.status) }}
            </div>
          </div>
          <div class="info-item">
            <label>优先级：</label>
            <div class="priority-tag" :class="getPriorityClass(task.priority)">
              {{ getPriorityName(task.priority) }}
            </div>
          </div>
          <div class="info-item">
            <label>发起人：</label>
            <span>{{ task.initiator }}</span>
          </div>
          <div class="info-item">
            <label>当前处理人：</label>
            <span>{{ task.assignee }}</span>
          </div>
          <div class="info-item">
            <label>到期时间：</label>
            <span :class="{ 'overdue': isOverdue(task.due_date) }">
              {{ formatDate(task.due_date) }}
            </span>
          </div>
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDate(task.created_at) }}</span>
          </div>
          <div class="info-item">
            <label>更新时间：</label>
            <span>{{ formatDate(task.updated_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 表单数据 -->
      <div class="detail-section">
        <h4>表单数据</h4>
        <div class="form-data">
          <div class="data-item">
            <label>申请类型：</label>
            <span>年假</span>
          </div>
          <div class="data-item">
            <label>开始日期：</label>
            <span>2024-01-20</span>
          </div>
          <div class="data-item">
            <label>结束日期：</label>
            <span>2024-01-22</span>
          </div>
          <div class="data-item">
            <label>请假天数：</label>
            <span>3天</span>
          </div>
          <div class="data-item">
            <label>请假原因：</label>
            <span>家庭事务处理</span>
          </div>
        </div>
      </div>

      <!-- 处理历史 -->
      <div class="detail-section">
        <h4>处理历史</h4>
        <div class="history-timeline">
          <div class="timeline-item">
            <div class="timeline-dot completed"></div>
            <div class="timeline-content">
              <div class="timeline-title">申请提交</div>
              <div class="timeline-meta">张三 · 2024-01-15 09:30</div>
              <div class="timeline-comment">提交请假申请</div>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-dot current"></div>
            <div class="timeline-content">
              <div class="timeline-title">部门主管审批</div>
              <div class="timeline-meta">李主管 · 待处理</div>
              <div class="timeline-comment">等待部门主管审批</div>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-dot pending"></div>
            <div class="timeline-content">
              <div class="timeline-title">HR确认</div>
              <div class="timeline-meta">待分配</div>
              <div class="timeline-comment">等待HR最终确认</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = ref(false)

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const getStatusClass = (status) => {
  const statusMap = {
    pending: 'status-warning',
    in_progress: 'status-info',
    completed: 'status-success',
    rejected: 'status-danger'
  }
  return statusMap[status] || 'status-info'
}

const getStatusName = (status) => {
  const statusMap = {
    pending: '待处理',
    in_progress: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

const getPriorityClass = (priority) => {
  const priorityMap = {
    high: 'priority-high',
    medium: 'priority-medium',
    low: 'priority-low'
  }
  return priorityMap[priority] || 'priority-medium'
}

const getPriorityName = (priority) => {
  const priorityMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || '中'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const isOverdue = (dueDate) => {
  if (!dueDate) return false
  return new Date(dueDate) < new Date()
}
</script>

<style lang="scss" scoped>
.task-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 2rem;

  h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    border-bottom: 1px solid var(--border-light);
    padding-bottom: 0.5rem;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.info-item,
.data-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  label {
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 80px;
  }

  span {
    color: var(--text-primary);

    &.overdue {
      color: var(--danger-color);
      font-weight: 600;
    }
  }
}

.form-data {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.history-timeline {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
  }

  .timeline-item {
    position: relative;
    padding-left: 2.5rem;
    margin-bottom: 1.5rem;

    .timeline-dot {
      position: absolute;
      left: 0.5rem;
      top: 0.25rem;
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
      border: 2px solid var(--border-color);
      background: var(--card-background);

      &.completed {
        background: var(--success-color);
        border-color: var(--success-color);
      }

      &.current {
        background: var(--primary-color);
        border-color: var(--primary-color);
      }

      &.pending {
        background: var(--card-background);
        border-color: var(--border-color);
      }
    }

    .timeline-content {
      .timeline-title {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
      }

      .timeline-meta {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-bottom: 0.25rem;
      }

      .timeline-comment {
        font-size: 0.875rem;
        color: var(--text-light);
      }
    }
  }
}

// 状态和优先级样式
.vuexy-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;

  .status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: currentColor;
  }

  &.status-success {
    background: var(--success-light);
    color: var(--success-color);
  }

  &.status-warning {
    background: var(--warning-light);
    color: var(--warning-color);
  }

  &.status-info {
    background: var(--info-light);
    color: var(--info-color);
  }

  &.status-danger {
    background: var(--danger-light);
    color: var(--danger-color);
  }
}

.priority-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;

  &.priority-high {
    background: rgba(234, 84, 85, 0.1);
    color: #ea5455;
  }

  &.priority-medium {
    background: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
  }

  &.priority-low {
    background: rgba(40, 199, 111, 0.1);
    color: #28c76f;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
