<template>
  <el-dialog
    v-model="dialogVisible"
    title="版本对比"
    width="1200px"
    :before-close="handleClose"
    class="vuexy-dialog version-compare-dialog"
  >
    <div v-if="version" class="version-compare">
      <!-- 版本选择器 -->
      <div class="compare-header">
        <div class="version-selector">
          <div class="selector-item">
            <label>基准版本</label>
            <el-select v-model="baseVersionId" placeholder="选择基准版本" size="large">
              <el-option
                v-for="v in availableVersions"
                :key="v.id"
                :label="`v${v.version}`"
                :value="v.id"
              >
                <span>v{{ v.version }}</span>
                <span style="color: #8492a6; font-size: 13px; margin-left: 8px">
                  {{ formatDate(v.created_at) }}
                </span>
              </el-option>
            </el-select>
          </div>
          <div class="compare-arrow">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M5 12h14" stroke="currentColor" stroke-width="2"/>
              <path d="M12 5l7 7-7 7" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="selector-item">
            <label>对比版本</label>
            <div class="current-version">
              <span>v{{ version.version }}</span>
              <span class="version-date">{{ formatDate(version.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 对比结果 -->
      <div v-if="baseVersionId && compareResult" class="compare-content">
        <!-- 统计信息 -->
        <div class="compare-stats">
          <div class="stat-card added">
            <div class="stat-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ compareResult.added.length }}</div>
              <div class="stat-label">新增项</div>
            </div>
          </div>
          
          <div class="stat-card modified">
            <div class="stat-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ compareResult.modified.length }}</div>
              <div class="stat-label">修改项</div>
            </div>
          </div>
          
          <div class="stat-card deleted">
            <div class="stat-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ compareResult.deleted.length }}</div>
              <div class="stat-label">删除项</div>
            </div>
          </div>
          
          <div class="stat-card unchanged">
            <div class="stat-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ compareResult.unchanged.length }}</div>
              <div class="stat-label">未变更</div>
            </div>
          </div>
        </div>

        <!-- 变更详情 -->
        <div class="change-details">
          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="新增项" name="added" :disabled="compareResult.added.length === 0">
              <div class="change-list">
                <div v-for="item in compareResult.added" :key="item.value" class="change-item added">
                  <div class="change-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                      <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </div>
                  <div class="change-content">
                    <div class="item-header">
                      <span class="item-label">{{ item.label }}</span>
                      <span class="item-value">{{ item.value }}</span>
                    </div>
                    <div v-if="item.description" class="item-description">{{ item.description }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="修改项" name="modified" :disabled="compareResult.modified.length === 0">
              <div class="change-list">
                <div v-for="change in compareResult.modified" :key="change.value" class="change-item modified">
                  <div class="change-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </div>
                  <div class="change-content">
                    <div class="item-header">
                      <span class="item-label">{{ change.current.label }}</span>
                      <span class="item-value">{{ change.current.value }}</span>
                    </div>
                    <div class="change-diff">
                      <div v-for="field in change.changes" :key="field" class="diff-item">
                        <span class="diff-field">{{ getFieldName(field) }}:</span>
                        <span class="diff-old">{{ change.old[field] || '(空)' }}</span>
                        <span class="diff-arrow">→</span>
                        <span class="diff-new">{{ change.current[field] || '(空)' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="删除项" name="deleted" :disabled="compareResult.deleted.length === 0">
              <div class="change-list">
                <div v-for="item in compareResult.deleted" :key="item.value" class="change-item deleted">
                  <div class="change-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                      <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </div>
                  <div class="change-content">
                    <div class="item-header">
                      <span class="item-label">{{ item.label }}</span>
                      <span class="item-value">{{ item.value }}</span>
                    </div>
                    <div v-if="item.description" class="item-description">{{ item.description }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="未变更" name="unchanged" :disabled="compareResult.unchanged.length === 0">
              <div class="change-list">
                <div v-for="item in compareResult.unchanged" :key="item.value" class="change-item unchanged">
                  <div class="change-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                  </div>
                  <div class="change-content">
                    <div class="item-header">
                      <span class="item-label">{{ item.label }}</span>
                      <span class="item-value">{{ item.value }}</span>
                    </div>
                    <div v-if="item.description" class="item-description">{{ item.description }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!baseVersionId" class="empty-state">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="empty-icon">
          <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2 2v-3M3 16v3a2 2 0 0 0 2 2h3" stroke="currentColor" stroke-width="2"/>
          <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
          <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
        </svg>
        <h3>选择基准版本</h3>
        <p>请选择一个基准版本来对比差异</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <button class="action-btn" @click="handleClose">
          关闭
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  version: {
    type: Object,
    default: null
  },
  dictionary: {
    type: Object,
    default: null
  },
  versions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const dialogVisible = ref(false)
const baseVersionId = ref('')
const activeTab = ref('added')

// 计算属性
const availableVersions = computed(() => {
  if (!props.versions || !props.version) return []
  return props.versions.filter(v => v.id !== props.version.id)
})

const baseVersion = computed(() => {
  if (!baseVersionId.value) return null
  return props.versions.find(v => v.id === baseVersionId.value)
})

const compareResult = computed(() => {
  if (!baseVersion.value || !props.version) return null
  
  const baseItems = getVersionItems(baseVersion.value)
  const currentItems = getVersionItems(props.version)
  
  return compareVersions(baseItems, currentItems)
})

// 方法
const getVersionItems = (version) => {
  if (!version?.items_data) return []
  
  try {
    const itemsData = typeof version.items_data === 'string' 
      ? JSON.parse(version.items_data) 
      : version.items_data
    return Array.isArray(itemsData) ? itemsData : []
  } catch {
    return []
  }
}

const compareVersions = (baseItems, currentItems) => {
  const baseMap = new Map(baseItems.map(item => [item.value, item]))
  const currentMap = new Map(currentItems.map(item => [item.value, item]))
  
  const added = []
  const modified = []
  const deleted = []
  const unchanged = []
  
  // 检查新增和修改
  for (const [value, currentItem] of currentMap) {
    const baseItem = baseMap.get(value)
    
    if (!baseItem) {
      added.push(currentItem)
    } else {
      const changes = getItemChanges(baseItem, currentItem)
      if (changes.length > 0) {
        modified.push({
          value,
          old: baseItem,
          current: currentItem,
          changes
        })
      } else {
        unchanged.push(currentItem)
      }
    }
  }
  
  // 检查删除
  for (const [value, baseItem] of baseMap) {
    if (!currentMap.has(value)) {
      deleted.push(baseItem)
    }
  }
  
  return { added, modified, deleted, unchanged }
}

const getItemChanges = (oldItem, newItem) => {
  const changes = []
  const fields = ['label', 'description', 'sort_order', 'status']
  
  for (const field of fields) {
    if (oldItem[field] !== newItem[field]) {
      changes.push(field)
    }
  }
  
  return changes
}

const getFieldName = (field) => {
  const fieldNames = {
    label: '标签',
    description: '描述',
    sort_order: '排序',
    status: '状态'
  }
  return fieldNames[field] || field
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    baseVersionId.value = ''
    activeTab.value = 'added'
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 监听对比结果变化，自动切换到有数据的标签页
watch(compareResult, (newResult) => {
  if (newResult) {
    if (newResult.added.length > 0) {
      activeTab.value = 'added'
    } else if (newResult.modified.length > 0) {
      activeTab.value = 'modified'
    } else if (newResult.deleted.length > 0) {
      activeTab.value = 'deleted'
    } else {
      activeTab.value = 'unchanged'
    }
  }
})
</script>

<style lang="scss" scoped>
.version-compare {
  .compare-header {
    margin-bottom: 1.5rem;
  }

  .version-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    
    .selector-item {
      flex: 1;
      
      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--text-color-primary);
      }
    }
    
    .compare-arrow {
      color: var(--text-color-secondary);
      margin-top: 1.5rem;
    }
    
    .current-version {
      height: 40px;
      padding: 0 1rem;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      background: var(--background-color-light);
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      span:first-child {
        font-weight: 600;
        color: var(--text-color-primary);
      }
      
      .version-date {
        font-size: 0.875rem;
        color: var(--text-color-secondary);
      }
    }
  }

  .compare-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
    
    .stat-card {
      background: var(--card-background);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 1rem;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      
      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }
      
      .stat-info {
        .stat-number {
          font-size: 1.25rem;
          font-weight: 600;
          line-height: 1;
          margin-bottom: 0.25rem;
        }
        
        .stat-label {
          font-size: 0.75rem;
          color: var(--text-color-secondary);
        }
      }
      
      &.added {
        .stat-icon {
          background: rgba(40, 199, 111, 0.1);
          color: #28c76f;
        }
        .stat-number {
          color: #28c76f;
        }
      }
      
      &.modified {
        .stat-icon {
          background: rgba(255, 159, 67, 0.1);
          color: #ff9f43;
        }
        .stat-number {
          color: #ff9f43;
        }
      }
      
      &.deleted {
        .stat-icon {
          background: rgba(234, 84, 85, 0.1);
          color: #ea5455;
        }
        .stat-number {
          color: #ea5455;
        }
      }
      
      &.unchanged {
        .stat-icon {
          background: rgba(108, 117, 125, 0.1);
          color: #6c757d;
        }
        .stat-number {
          color: #6c757d;
        }
      }
    }
  }

  .change-details {
    .change-list {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .change-item {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      padding: 1rem;
      border-bottom: 1px solid var(--border-color);
      
      &:last-child {
        border-bottom: none;
      }
      
      .change-icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }
      
      .change-content {
        flex: 1;
        
        .item-header {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin-bottom: 0.5rem;
          
          .item-label {
            font-weight: 600;
            color: var(--text-color-primary);
          }
          
          .item-value {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            color: var(--text-color-secondary);
            background: var(--background-color-light);
            padding: 0.125rem 0.375rem;
            border-radius: 4px;
            border: 1px solid var(--border-color);
          }
        }
        
        .item-description {
          color: var(--text-color-secondary);
          font-size: 0.875rem;
          line-height: 1.4;
        }
        
        .change-diff {
          .diff-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
            
            .diff-field {
              font-weight: 600;
              color: var(--text-color-primary);
              min-width: 60px;
            }
            
            .diff-old {
              color: #ea5455;
              text-decoration: line-through;
            }
            
            .diff-arrow {
              color: var(--text-color-secondary);
            }
            
            .diff-new {
              color: #28c76f;
              font-weight: 500;
            }
          }
        }
      }
      
      &.added {
        .change-icon {
          background: rgba(40, 199, 111, 0.1);
          color: #28c76f;
        }
      }
      
      &.modified {
        .change-icon {
          background: rgba(255, 159, 67, 0.1);
          color: #ff9f43;
        }
      }
      
      &.deleted {
        .change-icon {
          background: rgba(234, 84, 85, 0.1);
          color: #ea5455;
        }
      }
      
      &.unchanged {
        .change-icon {
          background: rgba(108, 117, 125, 0.1);
          color: #6c757d;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    
    .empty-icon {
      color: var(--text-color-secondary);
      margin-bottom: 1rem;
    }
    
    h3 {
      margin: 0 0 0.5rem 0;
      color: var(--text-color-primary);
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: var(--text-color-secondary);
      font-size: 0.875rem;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 36px;
  padding: 0 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}
</style>
