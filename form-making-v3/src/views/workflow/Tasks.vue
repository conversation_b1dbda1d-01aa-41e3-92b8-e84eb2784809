<template>
  <div class="vuexy-workflow-tasks">
    <!-- Vuexy 风格筛选区域 -->
    <div class="vuexy-filter-section">
      <div class="filter-left">
        <div class="vuexy-search-box">
          <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索任务标题、流程名称或发起人"
            class="search-input"
          />
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedStatus" class="vuexy-select">
            <option value="">全部状态</option>
            <option value="pending">待处理</option>
            <option value="in_progress">处理中</option>
            <option value="completed">已完成</option>
            <option value="rejected">已拒绝</option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedPriority" class="vuexy-select">
            <option value="">全部优先级</option>
            <option value="high">高优先级</option>
            <option value="medium">中优先级</option>
            <option value="low">低优先级</option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
      <div class="filter-right">
        <button @click="refreshTasks" class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
          刷新
        </button>
      </div>
    </div>

    <!-- 任务统计卡片 -->
    <div class="task-stats-grid">
      <div class="stat-card pending">
        <div class="stat-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ taskStats.pending }}</div>
          <div class="stat-label">待处理</div>
        </div>
      </div>
      <div class="stat-card in-progress">
        <div class="stat-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ taskStats.inProgress }}</div>
          <div class="stat-label">处理中</div>
        </div>
      </div>
      <div class="stat-card completed">
        <div class="stat-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <polyline points="9,12 12,15 16,11" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ taskStats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      <div class="stat-card overdue">
        <div class="stat-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M12 8v4l3 3" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ taskStats.overdue }}</div>
          <div class="stat-label">已逾期</div>
        </div>
      </div>
    </div>

    <!-- 改进的任务表格 -->
    <div class="vuexy-table" v-loading="loading">
      <el-table
        :data="filteredTasks"
        style="width: 100%"
        :stripe="false"
        :border="false"
        :highlight-current-row="true"
        @current-change="handleCurrentRow"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="任务信息" min-width="300">
          <template #default="scope">
            <div class="product-info">
              <div class="product-avatar">
                <div class="task-priority-indicator" :class="getPriorityClass(scope.row.priority)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
                  </svg>
                </div>
              </div>
              <div class="product-details">
                <div class="product-name">{{ scope.row.title }}</div>
                <div class="product-description">{{ scope.row.workflow_name }} - {{ scope.row.step_name }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="发起人" width="120">
          <template #default="scope">
            <div class="user-info">
              <div class="user-avatar">{{ scope.row.initiator.charAt(0) }}</div>
              <span class="user-name">{{ scope.row.initiator }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120">
          <template #default="scope">
            <div class="vuexy-status-tag" :class="getStatusClass(scope.row.status)">
              <div class="status-dot"></div>
              {{ getStatusName(scope.row.status) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="优先级" width="100">
          <template #default="scope">
            <div class="priority-tag" :class="getPriorityClass(scope.row.priority)">
              {{ getPriorityName(scope.row.priority) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="到期时间" width="150">
          <template #default="scope">
            <span class="due-date" :class="{ 'overdue': isOverdue(scope.row.due_date) }">
              {{ formatDate(scope.row.due_date) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="150">
          <template #default="scope">
            <span class="create-time">{{ formatDate(scope.row.created_at) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <div class="vuexy-table-actions">
              <el-button
                v-if="scope.row.status === 'pending'"
                type="primary"
                size="small"
                @click="handleTask(scope.row)"
                title="处理任务"
              >
                处理
              </el-button>
              <el-button
                size="small"
                @click="viewTask(scope.row)"
                title="查看详情"
              >
                详情
              </el-button>
              <el-button
                v-if="scope.row.status === 'pending'"
                type="warning"
                size="small"
                @click="transferTask(scope.row)"
                title="转派任务"
              >
                转派
              </el-button>
              <el-dropdown @command="handleTaskAction" trigger="click">
                <el-button size="small" title="更多操作">
                  更多
                  <el-icon class="el-icon--right">
                    <ArrowDown />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`history-${scope.row.id}`">查看历史</el-dropdown-item>
                    <el-dropdown-item :command="`comment-${scope.row.id}`">添加备注</el-dropdown-item>
                    <el-dropdown-item :command="`remind-${scope.row.id}`" divided>催办提醒</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 改进的分页器 -->
      <div class="vuexy-pagination" v-if="totalTasks > 0">
        <div class="pagination-info">
          显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalTasks) }} 条，共 {{ totalTasks }} 条记录
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalTasks"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 任务处理对话框 -->
    <TaskHandleDialog
      v-model:visible="handleDialogVisible"
      :task="currentTask"
      @handled="handleTaskHandled"
    />

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model:visible="detailDialogVisible"
      :task="currentTask"
    />

    <!-- 任务转派对话框 -->
    <TaskTransferDialog
      v-model:visible="transferDialogVisible"
      :task="currentTask"
      @transferred="handleTaskTransferred"
    />

    <!-- 任务历史对话框 -->
    <TaskHistoryDialog
      v-model:visible="historyDialogVisible"
      :task="currentTask"
    />

    <!-- 添加备注对话框 -->
    <TaskCommentDialog
      v-model:visible="commentDialogVisible"
      :task="currentTask"
      @commented="handleTaskCommented"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

// 导入对话框组件（这些组件需要创建）
import TaskHandleDialog from './components/TaskHandleDialog.vue'
import TaskDetailDialog from './components/TaskDetailDialog.vue'
import TaskTransferDialog from './components/TaskTransferDialog.vue'
import TaskHistoryDialog from './components/TaskHistoryDialog.vue'
import TaskCommentDialog from './components/TaskCommentDialog.vue'

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const searchQuery = ref('')
const selectedStatus = ref('')
const selectedPriority = ref('')

// 对话框状态
const handleDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const transferDialogVisible = ref(false)
const historyDialogVisible = ref(false)
const commentDialogVisible = ref(false)
const currentTask = ref(null)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const totalTasks = ref(0)

// 任务统计
const taskStats = ref({
  pending: 0,
  inProgress: 0,
  completed: 0,
  overdue: 0
})

// 计算属性
const filteredTasks = computed(() => {
  let filtered = tasks.value || []

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(task =>
      task.title.toLowerCase().includes(query) ||
      task.workflow_name.toLowerCase().includes(query) ||
      task.initiator.toLowerCase().includes(query)
    )
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(task => task.status === selectedStatus.value)
  }

  if (selectedPriority.value) {
    filtered = filtered.filter(task => task.priority === selectedPriority.value)
  }

  return filtered
})

// 方法
const loadTasks = async () => {
  try {
    loading.value = true

    // 模拟API调用 - 实际使用时替换为真实API
    const mockData = {
      list: [
        {
          id: 'T001',
          title: '张三的请假申请',
          workflow_name: '请假申请流程',
          step_name: '部门主管审批',
          status: 'pending',
          priority: 'medium',
          initiator: '张三',
          assignee: '李主管',
          due_date: '2024-01-20 18:00:00',
          created_at: '2024-01-15 09:30:00',
          updated_at: '2024-01-15 09:30:00'
        },
        {
          id: 'T002',
          title: '李四的采购申请',
          workflow_name: '采购申请流程',
          step_name: '财务审批',
          status: 'in_progress',
          priority: 'high',
          initiator: '李四',
          assignee: '王财务',
          due_date: '2024-01-18 17:00:00',
          created_at: '2024-01-15 08:45:00',
          updated_at: '2024-01-15 14:20:00'
        },
        {
          id: 'T003',
          title: '王五的报销申请',
          workflow_name: '报销申请流程',
          step_name: '已完成',
          status: 'completed',
          priority: 'low',
          initiator: '王五',
          assignee: '张会计',
          due_date: '2024-01-16 17:00:00',
          created_at: '2024-01-14 14:20:00',
          updated_at: '2024-01-15 11:30:00'
        },
        {
          id: 'T004',
          title: '赵六的设备申请',
          workflow_name: '设备申请流程',
          step_name: 'IT部门审批',
          status: 'pending',
          priority: 'high',
          initiator: '赵六',
          assignee: '刘IT',
          due_date: '2024-01-14 17:00:00', // 已逾期
          created_at: '2024-01-12 10:15:00',
          updated_at: '2024-01-12 10:15:00'
        }
      ],
      total: 4,
      stats: {
        pending: 2,
        inProgress: 1,
        completed: 1,
        overdue: 1
      }
    }

    tasks.value = mockData.list
    totalTasks.value = mockData.total
    taskStats.value = mockData.stats

  } catch (error) {
    console.error('加载任务失败:', error)
    ElMessage.error('加载任务失败')
  } finally {
    loading.value = false
  }
}

const refreshTasks = () => {
  currentPage.value = 1
  loadTasks()
}

// 任务操作方法
const handleTask = (task) => {
  currentTask.value = task
  handleDialogVisible.value = true
}

const viewTask = (task) => {
  currentTask.value = task
  detailDialogVisible.value = true
}

const transferTask = (task) => {
  currentTask.value = task
  transferDialogVisible.value = true
}

// 表格事件处理
const handleCurrentRow = (row) => {
  // 处理当前行选择
}

const handleSelectionChange = (selection) => {
  // 处理选择变化
}

const handleTaskAction = (command) => {
  const [action, id] = command.split('-')
  const task = tasks.value.find(t => t.id === id)

  switch (action) {
    case 'history':
      currentTask.value = task
      historyDialogVisible.value = true
      break
    case 'comment':
      currentTask.value = task
      commentDialogVisible.value = true
      break
    case 'remind':
      handleRemindTask(task)
      break
  }
}

// 对话框事件处理
const handleTaskHandled = () => {
  handleDialogVisible.value = false
  loadTasks()
  ElMessage.success('任务处理成功')
}

const handleTaskTransferred = () => {
  transferDialogVisible.value = false
  loadTasks()
  ElMessage.success('任务转派成功')
}

const handleTaskCommented = () => {
  commentDialogVisible.value = false
  loadTasks()
  ElMessage.success('备注添加成功')
}

const handleRemindTask = async (task) => {
  try {
    // 模拟催办提醒API调用
    ElMessage.success(`已向 ${task.assignee} 发送催办提醒`)
  } catch (error) {
    console.error('催办提醒失败:', error)
    ElMessage.error('催办提醒失败')
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadTasks()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadTasks()
}

// 工具方法
const getStatusClass = (status) => {
  const statusMap = {
    pending: 'status-warning',
    in_progress: 'status-info',
    completed: 'status-success',
    rejected: 'status-danger'
  }
  return statusMap[status] || 'status-info'
}

const getStatusName = (status) => {
  const statusMap = {
    pending: '待处理',
    in_progress: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

const getPriorityClass = (priority) => {
  const priorityMap = {
    high: 'priority-high',
    medium: 'priority-medium',
    low: 'priority-low'
  }
  return priorityMap[priority] || 'priority-medium'
}

const getPriorityName = (priority) => {
  const priorityMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || '中'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const isOverdue = (dueDate) => {
  if (!dueDate) return false
  return new Date(dueDate) < new Date()
}

// 组件挂载时加载数据
onMounted(() => {
  loadTasks()
})

// 监听搜索和筛选变化
watch([searchQuery, selectedStatus, selectedPriority], () => {
  currentPage.value = 1
}, { debounce: 300 })
</script>

<style lang="scss" scoped>
.vuexy-workflow-tasks {
  padding: 1.5rem;
  background: var(--background-color);
  min-height: 100vh;
}

// 筛选区域样式
.vuexy-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);

  .filter-left {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex: 1;
  }

  .filter-right {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
}

.vuexy-search-box {
  position: relative;
  min-width: 300px;

  .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    z-index: 1;
  }

  .search-input {
    background: var(--input-background);
    border: 2px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem 1rem 0.75rem 3rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    width: 300px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    &::placeholder {
      color: var(--text-light);
    }

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
    }

    &:hover {
      border-color: var(--text-light);
    }
  }
}

.vuexy-select-wrapper {
  position: relative;
  min-width: 150px;
  display: inline-block;

  .vuexy-select {
    background: var(--input-background);
    border: 2px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    min-width: 120px;
    width: 100%;
    appearance: none;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
    }

    &:hover {
      border-color: var(--text-light);
    }
  }

  .select-arrow {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    pointer-events: none;
    z-index: 2;
    width: 16px;
    height: 16px;
  }

  &:hover .select-arrow {
    color: var(--text-primary);
  }
}

// 任务统计卡片样式
.task-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
  }

  .stat-content {
    flex: 1;

    .stat-number {
      font-size: 1.75rem;
      font-weight: 600;
      line-height: 1;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--text-secondary);
    }
  }

  &.pending {
    .stat-icon {
      background: rgba(255, 159, 67, 0.1);
      color: #ff9f43;
    }
    .stat-number {
      color: #ff9f43;
    }
  }

  &.in-progress {
    .stat-icon {
      background: rgba(0, 207, 232, 0.1);
      color: #00cfe8;
    }
    .stat-number {
      color: #00cfe8;
    }
  }

  &.completed {
    .stat-icon {
      background: rgba(40, 199, 111, 0.1);
      color: #28c76f;
    }
    .stat-number {
      color: #28c76f;
    }
  }

  &.overdue {
    .stat-icon {
      background: rgba(234, 84, 85, 0.1);
      color: #ea5455;
    }
    .stat-number {
      color: #ea5455;
    }
  }
}

// 表格样式
.vuexy-table {
  background: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);
  overflow: hidden;

  :deep(.el-table) {
    background: transparent;

    .el-table__header {
      background: var(--table-header-background);

      th {
        background: transparent;
        border: none;
        color: var(--text-primary);
        font-weight: 600;
        font-size: 0.875rem;
        padding: 1rem 0.75rem;

        // 固定列头部样式
        &.is-fixed {
          background: var(--table-header-background) !important;
          z-index: 10;
        }
      }
    }

    .el-table__body {
      tr {
        background: transparent;

        &:hover {
          background: var(--hover-background);

          // 悬停时固定列背景
          .el-table__fixed-right {
            background: var(--hover-background) !important;
          }
        }

        td {
          border: none;
          padding: 1rem 0.75rem;
          color: var(--text-primary);

          // 固定列单元格样式
          &.is-fixed {
            background: var(--card-background) !important;
            z-index: 9;
          }
        }
      }
    }

    // 固定列容器样式
    .el-table__fixed,
    .el-table__fixed-right {
      background: var(--card-background) !important;
      box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
      z-index: 10 !important; // 提高z-index确保在其他内容之上

      .el-table__fixed-header-wrapper,
      .el-table__fixed-body-wrapper {
        background: var(--card-background) !important;
      }

      th,
      td {
        background: var(--card-background) !important;
      }

      // 悬停状态
      tr:hover td {
        background: var(--hover-background) !important;
      }
    }

    // 固定列右侧阴影
    .el-table__fixed-right::before {
      content: '';
      position: absolute;
      left: -8px;
      top: 0;
      bottom: 0;
      width: 8px;
      background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1));
      pointer-events: none;
      z-index: 1;
    }

    // 确保固定列内容不被遮挡
    .el-table__fixed-right-patch {
      background: var(--card-background) !important;
      z-index: 10 !important;
    }

    // 滚动时的固定列样式
    &.el-table--scrollable-x {
      .el-table__fixed-right {
        right: 0 !important;
        z-index: 10 !important;

        .el-table__fixed-header-wrapper,
        .el-table__fixed-body-wrapper {
          background: var(--card-background) !important;
          z-index: 10 !important;

          th,
          td {
            background: var(--card-background) !important;
            z-index: 10 !important;
            position: relative;
            z-index: 10;
          }
        }
      }
    }
  }
}

// 产品信息样式
.product-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  .product-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-rgba);
    color: var(--primary-color);
    flex-shrink: 0;

    .task-priority-indicator {
      width: 1.5rem;
      height: 1.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.priority-high {
        background: rgba(234, 84, 85, 0.1);
        color: #ea5455;
      }

      &.priority-medium {
        background: rgba(255, 159, 67, 0.1);
        color: #ff9f43;
      }

      &.priority-low {
        background: rgba(40, 199, 111, 0.1);
        color: #28c76f;
      }
    }
  }

  .product-details {
    flex: 1;
    min-width: 0;

    .product-name {
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 0.25rem;
      font-size: 0.875rem;
    }

    .product-description {
      color: var(--text-secondary);
      font-size: 0.75rem;
      line-height: 1.4;
    }
  }
}

// 用户信息样式
.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .user-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
  }

  .user-name {
    font-size: 0.875rem;
    color: var(--text-primary);
  }
}

// 状态标签样式
.vuexy-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;

  .status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: currentColor;
  }

  &.status-success {
    background: var(--success-light);
    color: var(--success-color);
  }

  &.status-warning {
    background: var(--warning-light);
    color: var(--warning-color);
  }

  &.status-info {
    background: var(--info-light);
    color: var(--info-color);
  }

  &.status-danger {
    background: var(--danger-light);
    color: var(--danger-color);
  }
}

// 优先级标签样式
.priority-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;

  &.priority-high {
    background: rgba(234, 84, 85, 0.1);
    color: #ea5455;
  }

  &.priority-medium {
    background: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
  }

  &.priority-low {
    background: rgba(40, 199, 111, 0.1);
    color: #28c76f;
  }
}

// 时间样式
.due-date {
  font-size: 0.875rem;
  color: var(--text-secondary);

  &.overdue {
    color: var(--danger-color);
    font-weight: 600;
  }
}

.create-time {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

// 操作按钮样式
.vuexy-table-actions {
  display: flex;
  gap: 0.375rem;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  position: relative;
  z-index: 10;

  :deep(.el-button) {
    margin: 0;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    height: auto;
    min-height: 1.75rem;
    border-radius: 0.25rem;
    position: relative;
    z-index: 11;

    &.el-button--small {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }

    &.el-button--primary {
      background: var(--primary-color);
      border-color: var(--primary-color);

      &:hover {
        background: var(--primary-dark);
        border-color: var(--primary-dark);
      }
    }

    &.el-button--warning {
      background: var(--warning-color);
      border-color: var(--warning-color);

      &:hover {
        background: var(--warning-dark);
        border-color: var(--warning-dark);
      }
    }

    &.el-button--default {
      background: var(--card-background);
      border-color: var(--border-color);
      color: var(--text-primary);

      &:hover {
        background: var(--hover-background);
        border-color: var(--text-light);
        color: var(--text-primary);
      }
    }
  }

  .el-dropdown {
    position: relative;
    z-index: 12;

    :deep(.el-button) {
      background: var(--card-background);
      border-color: var(--border-color);
      color: var(--text-primary);

      &:hover {
        background: var(--hover-background);
        border-color: var(--text-light);
        color: var(--text-primary);
      }
    }
  }
}

// 分页器样式
.vuexy-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-top: 1px solid var(--border-light);

  .pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
  }

  :deep(.el-pagination) {
    .el-pagination__total,
    .el-pagination__jump {
      color: var(--text-secondary);
    }

    .el-pager li {
      background: transparent;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);
      margin: 0 0.25rem;

      &.is-active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }

      &:hover:not(.is-active) {
        background: var(--hover-background);
        color: var(--text-primary);
      }
    }

    .btn-prev,
    .btn-next {
      background: transparent;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);

      &:hover {
        background: var(--hover-background);
        color: var(--text-primary);
      }
    }
  }
}

// 按钮样式
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background: var(--card-background);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;

  &:hover {
    background: var(--hover-background);
    border-color: var(--text-light);
    transform: translateY(-1px);
  }

  &.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);

    &:hover {
      background: var(--primary-dark);
      border-color: var(--primary-dark);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .vuexy-workflow-tasks {
    padding: 1rem;
  }

  .vuexy-filter-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .filter-left {
      flex-direction: column;
      gap: 0.75rem;
    }

    .filter-right {
      justify-content: center;
    }
  }

  .vuexy-search-box {
    min-width: auto;

    .search-input {
      width: 100%;
    }
  }

  .task-stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .vuexy-table-actions {
    flex-wrap: nowrap;
    gap: 0.25rem;
    overflow: hidden;

    :deep(.el-button) {
      padding: 0.125rem 0.375rem;
      font-size: 0.625rem;
      min-height: 1.5rem;
    }
  }

  .vuexy-pagination {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
</style>
