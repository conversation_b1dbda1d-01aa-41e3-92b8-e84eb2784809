const express = require('express');
const router = express.Router();
const dictionaryController = require('../controllers/dictionaryController');
const { validatePagination } = require('../middleware/validation');

// 字典分类相关路由
router.get('/categories', dictionaryController.getCategories);
router.post('/categories', dictionaryController.createCategory);
router.put('/categories/:id', dictionaryController.updateCategory);
router.delete('/categories/:id', dictionaryController.deleteCategory);

// 字典相关路由
router.get('/', validatePagination, dictionaryController.getDictionaries);
router.get('/:id', dictionaryController.getDictionary);
router.post('/', dictionaryController.createDictionary);
router.put('/:id', dictionaryController.updateDictionary);
router.delete('/:id', dictionaryController.deleteDictionary);

// 字典使用情况和版本历史
router.get('/:id/usage', dictionaryController.getDictionaryUsage);

// 版本管理路由
router.get('/:id/versions', dictionaryController.getDictionaryVersions);
router.post('/:id/versions', dictionaryController.createDictionaryVersion);
router.delete('/:id/versions/:versionId', dictionaryController.deleteDictionaryVersion);
router.post('/:id/versions/:versionId/rollback', dictionaryController.rollbackDictionaryVersion);

// 根据编码获取字典（供表单使用）
router.get('/code/:code', dictionaryController.getDictionaryByCode);

module.exports = router;
