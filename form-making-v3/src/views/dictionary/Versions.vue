<template>
  <div class="vuexy-dictionary-versions">
    <!-- 字典选择区域 -->
    <div class="vuexy-filter-section">
      <div class="filter-left">
        <div class="vuexy-select-wrapper">
          <select v-model="selectedCategoryId" class="vuexy-select" @change="onCategoryChange">
            <option value="">选择字典分类</option>
            <option
              v-for="category in categories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedDictionaryId" class="vuexy-select" @change="onDictionaryChange" :disabled="!selectedCategoryId">
            <option value="">选择字典</option>
            <option
              v-for="dictionary in filteredDictionaries"
              :key="dictionary.id"
              :value="dictionary.id"
            >
              {{ dictionary.name }} ({{ dictionary.code }})
            </option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="vuexy-search-box" v-if="selectedDictionaryId">
          <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索版本号或变更日志"
            class="search-input"
          />
        </div>
      </div>
      <div class="filter-right" v-if="selectedDictionaryId">
        <button @click="refreshVersions" class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
          刷新
        </button>
        <button @click="createVersion" class="action-btn primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
          </svg>
          创建版本
        </button>
      </div>
    </div>

    <!-- 字典信息卡片 -->
    <div v-if="currentDictionary" class="dictionary-info-card">
      <div class="card-header">
        <div class="dict-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="dict-details">
          <h3>{{ currentDictionary.name }}</h3>
          <p class="dict-code">{{ currentDictionary.code }}</p>
          <p class="dict-description">{{ currentDictionary.description || '暂无描述' }}</p>
        </div>
        <div class="dict-stats">
          <div class="stat-item">
            <div class="stat-number">{{ currentDictionary.version }}</div>
            <div class="stat-label">当前版本</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ versions.length }}</div>
            <div class="stat-label">历史版本</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本历史表格 -->
    <div v-if="selectedDictionaryId" class="vuexy-table">
      <el-table
        :data="filteredVersions"
        v-loading="loading"
        :stripe="false"
        :border="false"
        style="width: 100%"
      >
        <el-table-column label="版本信息" min-width="200">
          <template #default="{ row }">
            <div class="version-info">
              <div class="version-details">
                <div class="version-number">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="version-icon">
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    <path d="M12 1v6m0 6v6" stroke="currentColor" stroke-width="2"/>
                    <path d="m21 12-6-6-6 6-6-6" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  v{{ row.version }}
                  <span v-if="row.version === currentDictionary?.version" class="current-tag">当前</span>
                </div>
                <div class="version-date">{{ formatDate(row.created_at) }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="变更日志" min-width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="change-log">
              {{ row.change_log || '暂无变更日志' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="数据项数量" width="120" align="center">
          <template #default="{ row }">
            <span class="item-count">{{ getItemCount(row.items_data) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="创建者" width="120">
          <template #default="{ row }">
            <div class="creator-info">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="user-icon">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              </svg>
              {{ row.created_by || 'system' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="vuexy-table-actions">
              <button class="action-btn icon-only info-btn" @click="viewVersion(row)" title="查看详情">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  <path d="M21.21 15.89A10 10 0 1 1 8 2.83" stroke="currentColor" stroke-width="2"/>
                  <path d="M22 12h-4l-3 3h7l-3-3z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="action-btn icon-only primary-btn" @click="compareVersion(row)" title="对比差异">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" stroke="currentColor" stroke-width="2"/>
                  <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                  <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button
                v-if="row.version !== currentDictionary?.version"
                class="action-btn icon-only warning-btn"
                @click="rollbackVersion(row)"
                title="回滚到此版本"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <polyline points="1 4 1 10 7 10" stroke="currentColor" stroke-width="2"/>
                  <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="action-btn icon-only delete-btn" @click="deleteVersion(row)" title="删除版本">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                  <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div v-if="!selectedDictionaryId" class="empty-state">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="empty-icon">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
      </svg>
      <h3>选择字典</h3>
      <p>请先选择分类和字典，然后查看版本历史</p>
    </div>

    <!-- 版本详情对话框 -->
    <VersionDetail
      v-model:visible="detailVisible"
      :version="currentVersion"
      :dictionary="currentDictionary"
    />

    <!-- 版本对比对话框 -->
    <VersionCompare
      v-model:visible="compareVisible"
      :version="currentVersion"
      :dictionary="currentDictionary"
      :versions="versions"
    />

    <!-- 创建版本对话框 -->
    <VersionCreator
      v-model:visible="creatorVisible"
      :dictionary="currentDictionary"
      @created="handleVersionCreated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getDictionaryCategories,
  getDictionaries,
  getDictionary,
  getDictionaryVersions,
  deleteDictionaryVersion,
  rollbackDictionaryVersion
} from '@/util/api'
import VersionDetail from './components/VersionDetail.vue'
import VersionCompare from './components/VersionCompare.vue'
import VersionCreator from './components/VersionCreator.vue'

// 响应式数据
const loading = ref(false)
const categories = ref([])
const dictionaries = ref([])
const versions = ref([])
const searchQuery = ref('')
const selectedCategoryId = ref('')
const selectedDictionaryId = ref('')
const currentDictionary = ref(null)

// 对话框状态
const detailVisible = ref(false)
const compareVisible = ref(false)
const creatorVisible = ref(false)
const currentVersion = ref(null)

// 计算属性
const filteredDictionaries = computed(() => {
  if (!selectedCategoryId.value) return []
  return dictionaries.value.filter(dict => dict.category_id == selectedCategoryId.value)
})

const filteredVersions = computed(() => {
  if (!searchQuery.value) return versions.value

  const query = searchQuery.value.toLowerCase()
  return versions.value.filter(version =>
    version.version.toLowerCase().includes(query) ||
    (version.change_log && version.change_log.toLowerCase().includes(query))
  )
})

// 方法
const loadCategories = async () => {
  try {
    const response = await getDictionaryCategories()
    if (response.success) {
      categories.value = response.data || []
    }
  } catch (error) {
    console.error('加载字典分类失败:', error)
    ElMessage.error('加载字典分类失败')
  }
}

const loadDictionaries = async () => {
  try {
    const response = await getDictionaries({ limit: 1000 })
    if (response.success) {
      dictionaries.value = response.data.list || []
    }
  } catch (error) {
    console.error('加载字典列表失败:', error)
    ElMessage.error('加载字典列表失败')
  }
}

const loadDictionaryInfo = async () => {
  if (!selectedDictionaryId.value) return

  try {
    const response = await getDictionary(selectedDictionaryId.value)
    if (response.success) {
      currentDictionary.value = response.data
    }
  } catch (error) {
    console.error('加载字典信息失败:', error)
    ElMessage.error('加载字典信息失败')
  }
}

const loadVersions = async () => {
  if (!selectedDictionaryId.value) return

  try {
    loading.value = true
    const response = await getDictionaryVersions(selectedDictionaryId.value)
    if (response.success) {
      versions.value = response.data || []
    }
  } catch (error) {
    console.error('加载版本历史失败:', error)
    ElMessage.error('加载版本历史失败')
  } finally {
    loading.value = false
  }
}

const onCategoryChange = () => {
  selectedDictionaryId.value = ''
  currentDictionary.value = null
  versions.value = []
}

const onDictionaryChange = () => {
  if (selectedDictionaryId.value) {
    loadDictionaryInfo()
    loadVersions()
  } else {
    currentDictionary.value = null
    versions.value = []
  }
}

const refreshVersions = () => {
  loadVersions()
}

const createVersion = () => {
  creatorVisible.value = true
}

const viewVersion = (version) => {
  currentVersion.value = version
  detailVisible.value = true
}

const compareVersion = (version) => {
  currentVersion.value = version
  compareVisible.value = true
}

const rollbackVersion = async (version) => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚到版本 v${version.version} 吗？当前数据将被覆盖，此操作不可恢复。`,
      '确认回滚',
      {
        confirmButtonText: '确认回滚',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await rollbackDictionaryVersion(selectedDictionaryId.value, version.id)
    ElMessage.success('版本回滚成功')
    loadDictionaryInfo()
    loadVersions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('版本回滚失败:', error)
      ElMessage.error('版本回滚失败')
    }
  }
}

const deleteVersion = async (version) => {
  try {
    if (version.version === currentDictionary.value?.version) {
      ElMessage.warning('不能删除当前版本')
      return
    }

    await ElMessageBox.confirm(
      `确定要删除版本 v${version.version} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await deleteDictionaryVersion(selectedDictionaryId.value, version.id)
    ElMessage.success('版本删除成功')
    loadVersions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除版本失败:', error)
      ElMessage.error('删除版本失败')
    }
  }
}

const getItemCount = (itemsData) => {
  if (!itemsData) return 0
  try {
    const items = typeof itemsData === 'string' ? JSON.parse(itemsData) : itemsData
    return Array.isArray(items) ? items.length : 0
  } catch {
    return 0
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const handleVersionCreated = () => {
  creatorVisible.value = false
  loadDictionaryInfo()
  loadVersions()
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
  loadDictionaries()
})
</script>

<style lang="scss" scoped>
/* 版本管理页面样式 */
.vuexy-dictionary-versions {
  padding: 1.5rem;
}

/* 筛选区域样式 */
.vuexy-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 搜索框样式 */
.vuexy-search-box {
  position: relative;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  z-index: 2;
}

.search-input {
  width: 100%;
  height: 36px;
  padding: 0 16px 0 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

/* 选择器样式 */
.vuexy-select-wrapper {
  position: relative;
  min-width: 180px;
}

.vuexy-select {
  width: 100%;
  height: 36px;
  padding: 0 40px 0 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  cursor: pointer;
  appearance: none;
  transition: all 0.2s ease;
}

.vuexy-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

.vuexy-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  pointer-events: none;
}

/* 按钮样式 */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 36px;
  padding: 0 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.action-btn.icon-only {
  min-width: 36px;
  padding: 0;
}

.action-btn.edit-btn:hover {
  border-color: #ff9f43;
  color: #ff9f43;
  background: rgba(255, 159, 67, 0.08);
}

.action-btn.primary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.warning-btn:hover {
  border-color: #ff9f43;
  color: #ff9f43;
  background: rgba(255, 159, 67, 0.08);
}

.action-btn.success-btn:hover {
  border-color: #28c76f;
  color: #28c76f;
  background: rgba(40, 199, 111, 0.08);
}

.action-btn.delete-btn:hover {
  border-color: #ea5455;
  color: #ea5455;
  background: rgba(234, 84, 85, 0.08);
}

.action-btn.info-btn:hover {
  border-color: #00cfe8;
  color: #00cfe8;
  background: rgba(0, 207, 232, 0.08);
}

/* 表格样式 */
.vuexy-table {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.vuexy-table-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 状态标签样式 */
.vuexy-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.status-success {
  background: rgba(40, 199, 111, 0.1);
  color: #28c76f;
}

.status-secondary {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

/* 版本管理特有样式 */

/* 版本管理特有样式 */
.dictionary-info-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.dict-icon {
  width: 48px;
  height: 48px;
  background: rgba(115, 103, 240, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  flex-shrink: 0;
}

.dict-details {
  flex: 1;

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color-primary);
  }

  .dict-code {
    font-size: 0.875rem;
    color: var(--text-color-secondary);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    margin: 0 0 0.5rem 0;
  }

  .dict-description {
    font-size: 0.875rem;
    color: var(--text-color-secondary);
    margin: 0;
    line-height: 1.5;
  }
}

.dict-stats {
  display: flex;
  gap: 2rem;

  .stat-item {
    text-align: center;

    .stat-number {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--primary-color);
      line-height: 1;
    }

    .stat-label {
      font-size: 0.75rem;
      color: var(--text-color-secondary);
      margin-top: 0.25rem;
    }
  }
}

.version-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.version-details {
  flex: 1;
}

.version-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 0.25rem;

  .version-icon {
    color: var(--primary-color);
    flex-shrink: 0;
  }

  .current-tag {
    background: var(--primary-color);
    color: white;
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-weight: 500;
  }
}

.version-date {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
}

.change-log {
  color: var(--text-color-secondary);
  line-height: 1.4;
}

.item-count {
  font-weight: 600;
  color: var(--primary-color);
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-color-secondary);

  .user-icon {
    flex-shrink: 0;
  }
}

.empty-state {
  text-align: center;
  padding: 4rem 1rem;

  .empty-icon {
    color: var(--text-color-secondary);
    margin-bottom: 1rem;
  }

  h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color-primary);
    font-weight: 600;
  }

  p {
    margin: 0;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
  }
}

/* 暗色主题适配 */
html.dark .dictionary-info-card {
  background: #283046;
  border-color: #404656;
}

html.dark .dict-details h3 {
  color: #d0d2d6;
}

html.dark .dict-code,
html.dark .dict-description {
  color: #b4b7bd;
}

html.dark .version-number {
  color: #d0d2d6;
}

html.dark .version-date {
  color: #b4b7bd;
}

html.dark .change-log {
  color: #b4b7bd;
}

html.dark .item-count {
  color: var(--primary-color);
}

html.dark .creator-info {
  color: #b4b7bd;
}
</style>
