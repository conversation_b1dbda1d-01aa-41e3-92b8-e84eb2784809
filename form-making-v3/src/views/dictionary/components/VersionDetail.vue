<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`版本详情 - v${version?.version || ''}`"
    width="900px"
    :before-close="handleClose"
    class="vuexy-dialog"
  >
    <div v-if="version" class="version-detail">
      <!-- 版本基本信息 -->
      <div class="version-info-card">
        <div class="version-header">
          <div class="version-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              <path d="M12 1v6m0 6v6" stroke="currentColor" stroke-width="2"/>
              <path d="m21 12-6-6-6 6-6-6" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="version-details">
            <h3>版本 v{{ version.version }}</h3>
            <p class="version-meta">
              <span class="meta-item">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
                </svg>
                {{ formatDate(version.created_at) }}
              </span>
              <span class="meta-item">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                </svg>
                {{ version.created_by || 'system' }}
              </span>
              <span v-if="version.version === dictionary?.version" class="current-badge">当前版本</span>
            </p>
          </div>
          <div class="version-stats">
            <div class="stat-item">
              <div class="stat-number">{{ itemCount }}</div>
              <div class="stat-label">数据项</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 变更日志 -->
      <div class="change-log-section">
        <h4>变更日志</h4>
        <div class="change-log-content">
          <div v-if="version.change_log" class="log-text">
            {{ version.change_log }}
          </div>
          <div v-else class="no-log">
            暂无变更日志
          </div>
        </div>
      </div>

      <!-- 数据项列表 -->
      <div class="items-section">
        <div class="section-header">
          <h4>数据项列表</h4>
          <div class="section-actions">
            <div class="vuexy-search-box">
              <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
              </svg>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索数据项"
                class="search-input"
              />
            </div>
          </div>
        </div>

        <div class="items-table">
          <el-table
            :data="filteredItems"
            style="width: 100%"
            max-height="400"
            :stripe="false"
            :border="false"
          >
            <el-table-column label="标签" min-width="150">
              <template #default="{ row }">
                <div class="item-label">{{ row.label }}</div>
              </template>
            </el-table-column>

            <el-table-column label="值" min-width="150">
              <template #default="{ row }">
                <div class="item-value">{{ row.value }}</div>
              </template>
            </el-table-column>

            <el-table-column label="描述" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.description || '暂无描述' }}
              </template>
            </el-table-column>

            <el-table-column label="排序" width="80" align="center">
              <template #default="{ row }">
                <span class="sort-order">{{ row.sort_order || 0 }}</span>
              </template>
            </el-table-column>

            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <div class="vuexy-status-tag" :class="getVuexyStatusClass(row.status)">
                  <div class="status-dot"></div>
                  {{ getStatusName(row.status) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 空状态 -->
        <div v-if="!items.length" class="empty-items">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" class="empty-icon">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
          </svg>
          <p>该版本暂无数据项</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <button class="action-btn" @click="handleClose">
          关闭
        </button>
        <button 
          v-if="version?.version !== dictionary?.version"
          class="action-btn warning" 
          @click="rollbackToThisVersion"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="1 4 1 10 7 10" stroke="currentColor" stroke-width="2"/>
            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" stroke="currentColor" stroke-width="2"/>
          </svg>
          回滚到此版本
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { rollbackDictionaryVersion } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  version: {
    type: Object,
    default: null
  },
  dictionary: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'rollback'])

// 响应式数据
const dialogVisible = ref(false)
const searchQuery = ref('')

// 计算属性
const items = computed(() => {
  if (!props.version?.items_data) return []
  
  try {
    const itemsData = typeof props.version.items_data === 'string' 
      ? JSON.parse(props.version.items_data) 
      : props.version.items_data
    return Array.isArray(itemsData) ? itemsData : []
  } catch {
    return []
  }
})

const itemCount = computed(() => items.value.length)

const filteredItems = computed(() => {
  if (!searchQuery.value) return items.value
  
  const query = searchQuery.value.toLowerCase()
  return items.value.filter(item =>
    item.label.toLowerCase().includes(query) ||
    item.value.toLowerCase().includes(query) ||
    (item.description && item.description.toLowerCase().includes(query))
  )
})

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const getVuexyStatusClass = (status) => {
  const statusMap = {
    active: 'status-success',
    inactive: 'status-secondary'
  }
  return statusMap[status] || 'status-secondary'
}

const getStatusName = (status) => {
  const statusMap = {
    active: '已启用',
    inactive: '已停用'
  }
  return statusMap[status] || '未知'
}

const rollbackToThisVersion = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚到版本 v${props.version.version} 吗？当前数据将被覆盖，此操作不可恢复。`,
      '确认回滚',
      {
        confirmButtonText: '确认回滚',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await rollbackDictionaryVersion(props.dictionary.id, props.version.id)
    ElMessage.success('版本回滚成功')
    emit('rollback')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('版本回滚失败:', error)
      ElMessage.error('版本回滚失败')
    }
  }
}

const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    searchQuery.value = ''
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style lang="scss" scoped>
.version-detail {
  .version-info-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .version-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }

  .version-icon {
    width: 48px;
    height: 48px;
    background: rgba(115, 103, 240, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
  }

  .version-details {
    flex: 1;
    
    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-color-primary);
    }
    
    .version-meta {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin: 0;
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        font-size: 0.875rem;
        color: var(--text-color-secondary);
        
        svg {
          flex-shrink: 0;
        }
      }
      
      .current-badge {
        background: var(--primary-color);
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  .version-stats {
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--primary-color);
        line-height: 1;
      }
      
      .stat-label {
        font-size: 0.75rem;
        color: var(--text-color-secondary);
        margin-top: 0.25rem;
      }
    }
  }

  .change-log-section,
  .items-section {
    margin-bottom: 1.5rem;
    
    h4 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      font-weight: 600;
      color: var(--text-color-primary);
    }
  }

  .change-log-content {
    background: var(--background-color-light);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    
    .log-text {
      color: var(--text-color-primary);
      line-height: 1.6;
      white-space: pre-wrap;
    }
    
    .no-log {
      color: var(--text-color-secondary);
      font-style: italic;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    
    h4 {
      margin: 0;
    }
  }

  .items-table {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
  }

  .item-label {
    font-weight: 600;
    color: var(--text-color-primary);
  }

  .item-value {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    color: var(--text-color-secondary);
    background: var(--background-color-light);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    display: inline-block;
  }

  .sort-order {
    font-weight: 600;
    color: var(--text-color-primary);
  }

  .empty-items {
    text-align: center;
    padding: 2rem;
    
    .empty-icon {
      color: var(--text-color-secondary);
      margin-bottom: 1rem;
    }
    
    p {
      margin: 0;
      color: var(--text-color-secondary);
      font-size: 0.875rem;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* 复用样式 */
.vuexy-search-box {
  position: relative;
  width: 250px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  z-index: 2;
}

.search-input {
  width: 100%;
  height: 32px;
  padding: 0 16px 0 36px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 36px;
  padding: 0 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.warning {
  background: #ff9f43;
  border-color: #ff9f43;
  color: white;
}

.action-btn.warning:hover {
  background: #ff8510;
  border-color: #ff8510;
}

.vuexy-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.status-success {
  background: rgba(40, 199, 111, 0.1);
  color: #28c76f;
}

.status-secondary {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}
</style>
