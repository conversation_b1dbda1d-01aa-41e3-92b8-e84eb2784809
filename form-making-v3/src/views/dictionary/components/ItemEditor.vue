<template>
  <el-dialog
    v-model="dialogVisible"
    :title="item?.id ? '编辑字典项' : '添加字典项'"
    width="600px"
    :before-close="handleClose"
    class="vuexy-dialog item-editor-dialog"
    :close-on-click-modal="false"
  >
    <div class="vuexy-dialog-content">
      <el-form
        ref="formRef"
        :model="itemForm"
        :rules="formRules"
        label-width="100px"
        class="vuexy-form"
      >
        <el-form-item label="显示标签" prop="label" class="required-field">
          <el-input
            v-model="itemForm.label"
            placeholder="请输入显示标签"
            size="large"
          />
        </el-form-item>

        <el-form-item label="字典值" prop="value" class="required-field">
          <el-input
            v-model="itemForm.value"
            placeholder="请输入字典值"
            size="large"
          />
          <div class="form-tip">
            字典值用于程序内部使用，建议使用英文或数字
          </div>
        </el-form-item>

        <el-form-item label="父级项" prop="parent_id" v-if="availableParentItems.length > 0">
          <el-select
            v-model="itemForm.parent_id"
            placeholder="请选择父级项（可选）"
            size="large"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="parentItem in availableParentItems"
              :key="parentItem.id"
              :label="parentItem.label"
              :value="parentItem.id"
            >
              <span>{{ parentItem.label }}</span>
              <span style="color: #8492a6; font-size: 13px; margin-left: 8px">{{ parentItem.value }}</span>
            </el-option>
          </el-select>
          <div v-if="item?.parent_label" class="form-tip">
            当前父级项：{{ item.parent_label }}
          </div>
        </el-form-item>

        <el-form-item label="描述信息" prop="description">
          <el-input
            v-model="itemForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
            size="large"
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="itemForm.sort_order"
            :min="0"
            :max="9999"
            size="large"
            style="width: 200px"
          />
          <div class="form-tip">
            数值越小排序越靠前
          </div>
        </el-form-item>

        <el-form-item label="扩展数据" prop="extra_data">
          <el-input
            v-model="extraDataText"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的扩展数据（可选）"
            size="large"
          />
          <div class="form-tip">
            可选的JSON格式扩展数据，用于存储额外信息
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="itemForm.status" size="large">
            <el-radio value="active">启用</el-radio>
            <el-radio value="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="vuexy-dialog-footer">
        <button class="action-btn" @click="handleClose">
          取消
        </button>
        <button class="action-btn primary" @click="saveItem" :disabled="saving">
          <svg v-if="saving" width="16" height="16" viewBox="0 0 24 24" fill="none" class="animate-spin">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-opacity="0.3"/>
            <path d="M12 2a10 10 0 0 1 10 10" stroke="currentColor" stroke-width="4"/>
          </svg>
          {{ saving ? '保存中...' : '保存' }}
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { updateDictionary } from '@/util/api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  item: {
    type: Object,
    default: null
  },
  dictionary: {
    type: Object,
    default: null
  },
  items: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'saved'])

// 响应式数据
const dialogVisible = ref(false)
const saving = ref(false)
const formRef = ref(null)
const extraDataText = ref('')

// 表单数据
const itemForm = reactive({
  label: '',
  value: '',
  parent_id: null,
  description: '',
  sort_order: 0,
  extra_data: null,
  status: 'active'
})

// 表单验证规则
const formRules = {
  label: [
    { required: true, message: '请输入显示标签', trigger: 'blur' },
    { min: 1, max: 200, message: '显示标签长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入字典值', trigger: 'blur' },
    { min: 1, max: 500, message: '字典值长度在 1 到 500 个字符', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', message: '排序必须是数字', trigger: 'blur' }
  ]
}

// 计算可用的父级项（排除自己和子项）
const availableParentItems = computed(() => {
  if (!props.items) return []
  
  const flatItems = flattenItems(props.items)
  
  if (!props.item?.id) {
    return flatItems
  }
  
  // 排除自己和自己的子项
  const excludeIds = new Set([props.item.id])
  collectChildrenIds(props.item, excludeIds)
  
  return flatItems.filter(item => !excludeIds.has(item.id))
})

// 扁平化项目树
const flattenItems = (items, level = 0) => {
  const result = []
  items.forEach(item => {
    result.push({
      ...item,
      label: '　'.repeat(level) + item.label
    })
    if (item.children && item.children.length > 0) {
      result.push(...flattenItems(item.children, level + 1))
    }
  })
  return result
}

// 收集子项ID
const collectChildrenIds = (item, excludeIds) => {
  if (item.children) {
    item.children.forEach(child => {
      excludeIds.add(child.id)
      collectChildrenIds(child, excludeIds)
    })
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(itemForm, {
    label: '',
    value: '',
    parent_id: null,
    description: '',
    sort_order: 0,
    extra_data: null,
    status: 'active'
  })
  
  extraDataText.value = ''
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 初始化表单数据
const initFormData = () => {
  if (props.item) {
    if (props.item.id) {
      // 编辑模式
      Object.assign(itemForm, {
        label: props.item.label || '',
        value: props.item.value || '',
        parent_id: props.item.parent_id || null,
        description: props.item.description || '',
        sort_order: props.item.sort_order || 0,
        extra_data: props.item.extra_data || null,
        status: props.item.status || 'active'
      })
      
      extraDataText.value = props.item.extra_data ? JSON.stringify(props.item.extra_data, null, 2) : ''
    } else {
      // 新建子项模式
      Object.assign(itemForm, {
        label: '',
        value: '',
        parent_id: props.item.parent_id || null,
        description: '',
        sort_order: 0,
        extra_data: null,
        status: 'active'
      })
      
      extraDataText.value = ''
    }
  } else {
    resetForm()
  }
}

// 保存字典项
const saveItem = async () => {
  try {
    await formRef.value.validate()
    
    // 验证扩展数据JSON格式
    let extraData = null
    if (extraDataText.value.trim()) {
      try {
        extraData = JSON.parse(extraDataText.value)
      } catch (error) {
        ElMessage.error('扩展数据格式不正确，请输入有效的JSON格式')
        return
      }
    }
    
    saving.value = true
    
    const data = {
      ...itemForm,
      extra_data: extraData
    }
    
    // 获取当前字典的所有项目
    const currentItems = flattenItemTree(props.items)
    
    if (props.item?.id) {
      // 编辑模式 - 更新现有项目
      const itemIndex = currentItems.findIndex(item => item.id === props.item.id)
      if (itemIndex !== -1) {
        currentItems[itemIndex] = { ...currentItems[itemIndex], ...data }
      }
    } else {
      // 新建模式 - 添加新项目
      currentItems.push({
        ...data,
        id: Date.now() // 临时ID，后端会生成真实ID
      })
    }
    
    // 更新字典
    await updateDictionary(props.dictionary.id, {
      items: currentItems
    })
    
    ElMessage.success(props.item?.id ? '字典项更新成功' : '字典项创建成功')
    emit('saved')
    handleClose()
  } catch (error) {
    if (error !== 'validation failed') {
      console.error('保存字典项失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

// 扁平化字典项树
const flattenItemTree = (items) => {
  const result = []
  const flatten = (items, parentId = null) => {
    items.forEach(item => {
      result.push({
        id: item.id,
        label: item.label,
        value: item.value,
        description: item.description,
        sort_order: item.sort_order,
        parent_id: parentId,
        extra_data: item.extra_data,
        status: item.status
      })
      if (item.children && item.children.length > 0) {
        flatten(item.children, item.id)
      }
    })
  }
  flatten(items)
  return result
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initFormData()
  } else {
    resetForm()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})
</script>

<style lang="scss" scoped>
.vuexy-dialog-content {
  padding: 0;
}

.vuexy-form {
  .required-field :deep(.el-form-item__label::before) {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
}

.form-tip {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
  margin-top: 0.25rem;
  line-height: 1.4;
}

.vuexy-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 0 0 0;
  border-top: 1px solid var(--border-color);
  margin-top: 1.5rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 36px;
  padding: 0 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: #ffffff;
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn:disabled:hover {
  border-color: var(--border-color);
  color: var(--text-color-primary);
  background: #ffffff;
}

.action-btn.primary:disabled:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 暗色主题适配 */
html.dark .action-btn {
  background: #283046;
  border-color: #404656;
  color: #d0d2d6;
}

html.dark .action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

html.dark .action-btn:disabled:hover {
  border-color: #404656;
  color: #d0d2d6;
  background: #283046;
}

html.dark .form-tip {
  color: #b4b7bd;
}

html.dark .vuexy-dialog-footer {
  border-top-color: #404656;
}
</style>
