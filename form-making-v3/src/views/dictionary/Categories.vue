<template>
  <div class="vuexy-dictionary-categories">
    <!-- Vuexy 风格筛选区域 -->
    <div class="vuexy-filter-section">
      <div class="filter-left">
        <div class="vuexy-search-box">
          <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索分类名称、编码或描述"
            class="search-input"
          />
        </div>
        <div class="vuexy-select-wrapper">
          <select v-model="selectedStatus" class="vuexy-select">
            <option value="">全部状态</option>
            <option value="active">已启用</option>
            <option value="inactive">已停用</option>
          </select>
          <svg class="select-arrow" width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
      <div class="filter-right">
        <button @click="refreshCategories" class="action-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <polyline points="23 4 23 10 17 10" stroke="currentColor" stroke-width="2"/>
            <polyline points="1 20 1 14 7 14" stroke="currentColor" stroke-width="2"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
          </svg>
          刷新
        </button>
        <button @click="createCategory" class="action-btn primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
          </svg>
          创建分类
        </button>
      </div>
    </div>

    <!-- 分类树形表格 -->
    <div class="vuexy-table">
      <el-table
        :data="filteredCategories"
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :stripe="false"
        :border="false"
        style="width: 100%"
        default-expand-all
      >
        <el-table-column label="分类信息" min-width="300">
          <template #default="{ row }">
            <div class="category-info">
              <div class="category-details">
                <div class="category-name">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="category-icon">
                    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  {{ row.name }}
                </div>
                <div class="category-code">{{ row.code }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="描述信息" min-width="200">
          <template #default="{ row }">
            <div class="description-info">
              <div class="description-text">
                {{ row.description || '暂无描述' }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="排序" width="80" align="center">
          <template #default="{ row }">
            <span class="sort-order">{{ row.sort_order }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <div class="vuexy-status-tag" :class="getVuexyStatusClass(row.status)">
              <div class="status-dot"></div>
              {{ getStatusName(row.status) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="字典数量" width="100" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="viewCategoryDictionaries(row)">
              {{ row.dictionary_count || 0 }} 个
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="vuexy-table-actions">
              <button class="action-btn icon-only edit-btn" @click="editCategory(row)" title="编辑">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2"/>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="action-btn icon-only primary-btn" @click="addSubCategory(row)" title="添加子分类">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                  <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button
                class="action-btn icon-only"
                :class="row.status === 'active' ? 'warning-btn' : 'success-btn'"
                @click="toggleCategoryStatus(row)"
                :title="row.status === 'active' ? '停用' : '启用'"
              >
                <svg v-if="row.status === 'active'" width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                  <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
                </svg>
                <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="action-btn icon-only delete-btn" @click="deleteCategory(row)" title="删除">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                  <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分类编辑对话框 -->
    <CategoryEditor
      v-model:visible="editorVisible"
      :category="currentCategory"
      :categories="categories"
      @saved="handleCategorySaved"
    />

    <!-- 分类字典列表对话框 -->
    <CategoryDictionaries
      v-model:visible="dictionariesVisible"
      :category="currentCategory"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  getDictionaryCategories,
  updateDictionaryCategory,
  deleteDictionaryCategory
} from '@/util/api'
import CategoryEditor from './components/CategoryEditor.vue'
import CategoryDictionaries from './components/CategoryDictionaries.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const categories = ref([])
const searchQuery = ref('')
const selectedStatus = ref('')

// 对话框状态
const editorVisible = ref(false)
const dictionariesVisible = ref(false)
const currentCategory = ref(null)

// 计算属性
const filteredCategories = computed(() => {
  let filtered = categories.value || []

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filterCategoriesRecursive(filtered, query)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(cat => cat.status === selectedStatus.value)
  }

  return filtered
})

// 递归过滤分类
const filterCategoriesRecursive = (categories, query) => {
  return categories.filter(category => {
    const matches = category.name.toLowerCase().includes(query) ||
                   category.code.toLowerCase().includes(query) ||
                   (category.description && category.description.toLowerCase().includes(query))

    if (matches) return true

    if (category.children && category.children.length > 0) {
      category.children = filterCategoriesRecursive(category.children, query)
      return category.children.length > 0
    }

    return false
  })
}

// 方法
const loadCategories = async () => {
  try {
    loading.value = true
    const response = await getDictionaryCategories()
    if (response.success) {
      categories.value = buildCategoryTree(response.data || [])
    }
  } catch (error) {
    console.error('加载字典分类失败:', error)
    ElMessage.error('加载字典分类失败')
  } finally {
    loading.value = false
  }
}

// 构建分类树
const buildCategoryTree = (categories) => {
  const categoryMap = new Map()
  const rootCategories = []

  // 先创建所有分类的映射
  categories.forEach(category => {
    category.children = []
    category.dictionary_count = 0 // 这里可以从API获取实际数量
    categoryMap.set(category.id, category)
  })

  // 构建树形结构
  categories.forEach(category => {
    if (category.parent_id) {
      const parent = categoryMap.get(category.parent_id)
      if (parent) {
        parent.children.push(category)
      }
    } else {
      rootCategories.push(category)
    }
  })

  return rootCategories
}

const refreshCategories = () => {
  loadCategories()
}

const createCategory = () => {
  currentCategory.value = null
  editorVisible.value = true
}

const editCategory = (category) => {
  currentCategory.value = category
  editorVisible.value = true
}

const addSubCategory = (parentCategory) => {
  currentCategory.value = {
    parent_id: parentCategory.id,
    parent_name: parentCategory.name
  }
  editorVisible.value = true
}

const toggleCategoryStatus = async (category) => {
  try {
    const newStatus = category.status === 'active' ? 'inactive' : 'active'
    const action = newStatus === 'active' ? '启用' : '停用'

    await ElMessageBox.confirm(
      `确定要${action}分类 "${category.name}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await updateDictionaryCategory(category.id, { status: newStatus })
    ElMessage.success(`分类${action}成功`)
    loadCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换分类状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const deleteCategory = async (category) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await deleteDictionaryCategory(category.id)
    ElMessage.success('分类删除成功')
    loadCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const viewCategoryDictionaries = (category) => {
  currentCategory.value = category
  dictionariesVisible.value = true
}

const handleCategorySaved = () => {
  editorVisible.value = false
  loadCategories()
}

const getVuexyStatusClass = (status) => {
  const statusMap = {
    active: 'status-success',
    inactive: 'status-secondary'
  }
  return statusMap[status] || 'status-secondary'
}

const getStatusName = (status) => {
  const statusMap = {
    active: '已启用',
    inactive: '已停用'
  }
  return statusMap[status] || '未知'
}

// 监听搜索和筛选变化
watch([searchQuery, selectedStatus], () => {
  // 实时过滤，不需要重新加载数据
}, { debounce: 300 })

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
})
</script>

<style lang="scss" scoped>
/* 字典分类页面样式 */
.vuexy-dictionary-categories {
  padding: 1.5rem;
}

/* 筛选区域样式 */
.vuexy-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 搜索框样式 */
.vuexy-search-box {
  position: relative;
  min-width: 250px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  z-index: 2;
}

.search-input {
  width: 100%;
  height: 36px;
  padding: 0 16px 0 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

/* 选择器样式 */
.vuexy-select-wrapper {
  position: relative;
  min-width: 180px;
}

.vuexy-select {
  width: 100%;
  height: 36px;
  padding: 0 40px 0 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  cursor: pointer;
  appearance: none;
  transition: all 0.2s ease;
}

.vuexy-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(115, 103, 240, 0.1);
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  pointer-events: none;
}

/* 按钮样式 */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 36px;
  padding: 0 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-background);
  color: var(--text-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  gap: 0.375rem;
}

.action-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

.action-btn.icon-only {
  min-width: 36px;
  padding: 0;
}

.action-btn.edit-btn:hover {
  border-color: #ff9f43;
  color: #ff9f43;
  background: rgba(255, 159, 67, 0.08);
}

.action-btn.primary-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(115, 103, 240, 0.08);
}

.action-btn.warning-btn:hover {
  border-color: #ff9f43;
  color: #ff9f43;
  background: rgba(255, 159, 67, 0.08);
}

.action-btn.success-btn:hover {
  border-color: #28c76f;
  color: #28c76f;
  background: rgba(40, 199, 111, 0.08);
}

.action-btn.delete-btn:hover {
  border-color: #ea5455;
  color: #ea5455;
  background: rgba(234, 84, 85, 0.08);
}

/* 表格样式 */
.vuexy-table {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.vuexy-table-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 状态标签样式 */
.vuexy-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.status-success {
  background: rgba(40, 199, 111, 0.1);
  color: #28c76f;
}

.status-secondary {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

/* 分类特有样式 */
.category-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.category-details {
  flex: 1;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 0.25rem;
}

.category-icon {
  color: var(--primary-color);
  flex-shrink: 0;
}

.category-code {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: var(--background-color-light);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.sort-order {
  font-weight: 600;
  color: var(--text-color-primary);
}

/* 树形表格样式调整 */
:deep(.el-table__expand-icon) {
  color: var(--primary-color);
}

:deep(.el-table__indent) {
  padding-left: 20px;
}

/* 暗色主题适配 */
html.dark .category-code {
  background: #3b4253;
  border-color: #404656;
  color: #b4b7bd;
}

html.dark .category-name {
  color: #d0d2d6;
}

html.dark .sort-order {
  color: #d0d2d6;
}
</style>
